# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-29 12:24+0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: core/admin.py:42
msgid "Информация за контакта"
msgstr ""

#: core/admin.py:45
msgid "Запитване"
msgstr ""

#: core/admin.py:46
msgid "Статус"
msgstr ""

#: core/admin.py:129 core/admin.py:196
msgid "Основни параметри"
msgstr ""

#: core/admin.py:138
msgid "Секция история"
msgstr ""

#: core/admin.py:147
msgid "Секция ценности"
msgstr ""

#: core/admin.py:156
msgid "Секция на нашия екип"
msgstr ""

#: core/admin.py:165
msgid "Секция призив към действие"
msgstr ""

#: core/admin.py:205
msgid "Контакт"
msgstr ""

#: core/admin.py:209 core/models.py:36
msgid "Съобщение"
msgstr ""

#: core/admin.py:218
msgid "Карта"
msgstr ""

#: core/management/commands/backup_site_data.py:24
msgid "Създава backup на всички данни от core и siteconfig моделите"
msgstr ""

#: core/management/commands/backup_site_data.py:31
msgid "Име на изходния файл (по подразбиране: site_data_backup.json)"
msgstr ""

#: core/management/commands/backup_site_data.py:36
msgid "Форматира JSON файла за по-лесно четене"
msgstr ""

#: core/management/commands/backup_site_data.py:43
msgid "Започвам създаването на backup..."
msgstr ""

#: core/management/commands/clear_test_data.py:30
msgid "Изчиства всички данни от базата данни"
msgstr ""

#: core/management/commands/clear_test_data.py:36
msgid "Потвърдете, че искате да изчистите всички данни"
msgstr ""

#: core/management/commands/clear_test_data.py:41
msgid "Запазва данните за градове, локации, типове имоти, екстри и екип"
msgstr ""

#: core/management/commands/clear_test_data.py:46
msgid "Запазва данните за секциите на сайта и настройките"
msgstr ""

#: core/management/commands/clear_test_data.py:54
msgid ""
"ВНИМАНИЕ: Тази команда ще изтрие всички данни от базата!\n"
"Това включва:\n"
"- Всички имоти, техните снимки и видеа\n"
"- Всички запитвания от клиенти\n"
"- Всички отзиви\n"
"- Всички градове и локации\n"
"- Всички типове имоти и техните характеристики\n"
"- Всички членове на екипа\n"
"- Всички секции на сайта (Hero, Featured, Why Us, CTA, About)\n"
"- Настройките на сайта\n"
"- Политики за поверителност и условия за ползване\n"
"\n"
"За да потвърдите, изпълнете командата с --confirm флаг:\n"
"python manage.py clear_test_data --confirm\n"
"\n"
"Ако искате да запазите градове, локации, типове имоти, екстри и екип, "
"използвайте:\n"
"python manage.py clear_test_data --confirm --preserve-cities\n"
"\n"
"Ако искате да запазите и секциите на сайта и настройките, използвайте:\n"
"python manage.py clear_test_data --confirm --preserve-cities --preserve-"
"sections"
msgstr ""

#: core/management/commands/clear_test_data.py:76
msgid "Започвам изчистването на данните..."
msgstr ""

#: core/management/commands/clear_test_data.py:132
msgid "Успешно изтрити:\n"
msgstr ""

#: core/management/commands/clear_test_data.py:180
msgid ""
"\n"
"\n"
"Запазени са:\n"
msgstr ""

#: core/management/commands/create_test_properties.py:18
msgid "Създава тестови данни за имоти в базата данни"
msgstr ""

#: core/management/commands/create_test_properties.py:25
msgid "Брой имоти за създаване (по подразбиране: 20)"
msgstr ""

#: core/management/commands/create_test_properties.py:31
msgid "Започвам създаването на тестови данни..."
msgstr ""

#: core/management/commands/create_test_properties.py:466
msgid ""
"Няма достатъчно данни за създаване на имоти. Моля, проверете дали са "
"създадени локации, типове и брокери."
msgstr ""

#: core/management/commands/create_test_properties.py:508
msgid ""
"Забележка: Този скрипт не създава автоматично снимки на имоти, тъй като "
"изисква наличието на файлове с изображения. Моля, добавете ръчно логика за "
"създаване на PropertyImage обекти, ако е необходимо."
msgstr ""

#: core/management/commands/create_test_sections.py:22
msgid ""
"Създава тестови секции за началната страница (Hero, Featured, Why Us, CTA)"
msgstr ""

#: core/management/commands/create_test_sections.py:26
msgid "Започвам създаването на тестови секции..."
msgstr ""

#: core/management/commands/create_test_sections.py:39
msgid "Всички секции са създадени / актуализирани успешно."
msgstr ""

#: core/management/commands/restore_site_data.py:23
msgid "Възстановява данни от backup файл създаден с backup_site_data командата"
msgstr ""

#: core/management/commands/restore_site_data.py:29
msgid "Път към backup файла"
msgstr ""

#: core/management/commands/restore_site_data.py:34
msgid "Изтрива съществуващите данни преди възстановяване"
msgstr ""

#: core/management/commands/restore_site_data.py:39
msgid "Потвърждава операцията без допълнителни въпроси"
msgstr ""

#: core/management/commands/restore_site_data.py:51
msgid ""
"ВНИМАНИЕ: Тази команда ще възстанови данни от backup файл!\n"
"Това може да промени или замени съществуващи данни.\n"
"\n"
"За да потвърдите, изпълнете командата с --confirm флаг:\n"
msgstr ""

#: core/management/commands/restore_site_data.py:84
msgid "Започвам възстановяването на данни..."
msgstr ""

#: core/models.py:13 core/models.py:33
msgid "Име"
msgstr ""

#: core/models.py:15 properties/models.py:210
msgid "Снимка"
msgstr ""

#: core/models.py:17 core/models.py:35
msgid "Телефонен номер"
msgstr ""

#: core/models.py:18 core/models.py:34
msgid "Имейл адрес"
msgstr ""

#: core/models.py:19
msgid "Длъжност"
msgstr ""

#: core/models.py:22
msgid "Член на екипа"
msgstr ""

#: core/models.py:23
msgid "Членове на екипа"
msgstr ""

#: core/models.py:42
msgid "Имот (ако е за конкретна обява)"
msgstr ""

#: core/models.py:45
msgid "Дата на получаване"
msgstr ""

#: core/models.py:48
msgid "Обработено запитване"
msgstr ""

#: core/models.py:52
msgid "Запитване за контакт"
msgstr ""

#: core/models.py:53
msgid "Запитвания за контакт"
msgstr ""

#: core/models.py:66 core/models.py:80 core/models.py:95 core/models.py:115
#: core/models.py:131 core/models.py:214
msgid "Заглавие"
msgstr ""

#: core/models.py:67 core/models.py:81 core/models.py:96 core/models.py:132
#: core/models.py:215
msgid "Подзаглавие"
msgstr ""

#: core/models.py:68 core/models.py:82 core/models.py:94 core/models.py:114
#: core/models.py:130
msgid "Активна"
msgstr ""

#: core/models.py:71 core/models.py:72
msgid "1. Секция"
msgstr ""

#: core/models.py:85 core/models.py:86
#, fuzzy
#| msgid "Все още няма препоръчани имоти."
msgid "2. Препоръчани Имоти"
msgstr "Δεν υπάρχουν ακόμη προτεινόμενα ακίνητα."

#: core/models.py:99 core/models.py:100
msgid "3. Защо да изберете нас?"
msgstr ""

#: core/models.py:112
msgid "Секция"
msgstr ""

#: core/models.py:116
msgid "Описание"
msgstr ""

#: core/models.py:117
msgid "Иконка"
msgstr ""

#: core/models.py:118 properties/models.py:213 properties/models.py:348
msgid "Поредност"
msgstr ""

#: core/models.py:121
msgid "Картичка"
msgstr ""

#: core/models.py:122
msgid "Картички"
msgstr ""

#: core/models.py:135 core/models.py:136
msgid "4. CTA"
msgstr ""

#: core/models.py:218
msgid "Заглавие Контакт"
msgstr ""

#: core/models.py:222
msgid "Заглавие Съобщение"
msgstr ""

#: core/models.py:225
msgid "Подзаглавие Съобщение"
msgstr ""

#: core/models.py:228
msgid "Заглавие Карта"
msgstr ""

#: core/models.py:230
msgid "Подзаглавие Карта"
msgstr ""

#: core/models.py:234 core/models.py:235
msgid "Страница Контакти"
msgstr ""

#: core/templates/core/base.html:10 core/templates/core/index.html:5
msgid "Имоти Bulgaria - Намерете вашия мечтан дом"
msgstr ""

#: core/templates/core/base.html:14
msgid ""
"Професионални услуги за недвижими имоти в България. Апартаменти, къщи, офиси "
"за продажба и под наем."
msgstr ""

#: core/templates/core/base.html:16
msgid "недвижими имоти, апартаменти, къщи, продажба, наем, България"
msgstr ""

#: core/templates/core/sections/cta_section.html:10
#: core/templates/core/sections/hero_section.html:49
msgid "Търси имоти"
msgstr "Αναζήτηση Ακινήτων"

#: core/templates/core/sections/cta_section.html:14
msgid "Свържи се с нас"
msgstr "Επικοινωνήστε μαζί μας"

#: core/templates/core/sections/featured_properties_section.html:20
msgid "Виж всички имоти"
msgstr "Δείτε όλα τα ακίνητα"

#: core/templates/core/sections/featured_properties_section.html:24
msgid "Все още няма препоръчани имоти."
msgstr "Δεν υπάρχουν ακόμη προτεινόμενα ακίνητα."

#: core/templates/core/sections/featured_properties_section.html:26
msgid "Разгледайте всички имоти"
msgstr "Περιηγηθείτε σε όλα τα ακίνητα"

#: core/templates/core/sections/hero_section.html:17
msgid "Локация"
msgstr "Τοποθεσία"

# New UI strings
#: core/templates/core/sections/hero_section.html:22
msgid "Избери локация"
msgstr "Επιλέξτε Τοποθεσία"

#: core/templates/core/sections/hero_section.html:29 properties/models.py:107
#: properties/models.py:141
msgid "Тип имот"
msgstr "Τύπος Ακινήτου"

#: core/templates/core/sections/hero_section.html:34
msgid "Всички типове"
msgstr "Όλοι οι Τύποι"

#: core/templates/partials/_header.html:23
#: core/templates/partials/_header.html:199
msgid "Начало"
msgstr ""

#: core/templates/partials/_header.html:27
#: core/templates/partials/_header.html:212 properties/models.py:180
msgid "Имоти"
msgstr ""

#: core/templates/partials/_header.html:31
#: core/templates/partials/_header.html:225
msgid "За нас"
msgstr ""

#: core/templates/partials/_header.html:35
#: core/templates/partials/_header.html:238
msgid "Контакти"
msgstr ""

#: core/templates/partials/_header.html:47
msgid "Избор на език"
msgstr ""

#: core/templates/partials/_header.html:106
msgid "Превключване на тема"
msgstr ""

#: core/templates/partials/_header.html:128
msgid "Отваряне на меню"
msgstr ""

#: core/templates/partials/_header.html:175
msgid "Меню"
msgstr ""

#: core/templates/partials/_header.html:254
msgid "Език"
msgstr ""

#: core/templates/partials/_header.html:299
msgid "Тема на интерфейса"
msgstr ""

#: properties/models.py:38
msgid "Име на града"
msgstr ""

#: properties/models.py:41 properties/models.py:54
msgid "Град"
msgstr ""

#: properties/models.py:42
msgid "Градове"
msgstr ""

# New UI strings
#: properties/models.py:52
#, fuzzy
#| msgid "Избери локация"
msgid "Име на локацията"
msgstr "Επιλέξτε Τοποθεσία"

#: properties/models.py:57
msgid "URL на Google Map"
msgstr ""

#: properties/models.py:60
msgid "Google Map iframe"
msgstr ""

#: properties/models.py:64
msgid ""
"Поставете тук пълният iframe код от Google Maps. Данните ще се извлекат "
"автоматично."
msgstr ""

#: properties/models.py:69 properties/models.py:145
msgid "Адрес"
msgstr ""

#: properties/models.py:70
msgid "Адреси"
msgstr ""

#: properties/models.py:97
msgid "Име на типа"
msgstr ""

#: properties/models.py:103 properties/models.py:138
msgid "URL адрес"
msgstr ""

#: properties/models.py:108
#, fuzzy
#| msgid "Тип имот"
msgid "Типове имоти"
msgstr "Τύπος Ακινήτου"

#: properties/models.py:118
msgid "Име на екстра"
msgstr ""

#: properties/models.py:121
msgid "Екстра"
msgstr ""

#: properties/models.py:122 properties/models.py:160
msgid "Екстри"
msgstr ""

#: properties/models.py:132
msgid "Заглавие на обявата"
msgstr ""

#: properties/models.py:147
msgid "Цена"
msgstr ""

#: properties/models.py:155
msgid "Валута"
msgstr ""

#: properties/models.py:157
msgid "Площ"
msgstr ""

#: properties/models.py:158
msgid "Брой стаи"
msgstr ""

#: properties/models.py:159
msgid "Пълно описание"
msgstr ""

#: properties/models.py:164
msgid "Отговорен брокер"
msgstr ""

#: properties/models.py:168
msgid "Публикувана обява"
msgstr ""

#: properties/models.py:170
msgid "Топ оферта"
msgstr ""

#: properties/models.py:172 properties/models.py:264 properties/models.py:360
msgid "Дата на създаване"
msgstr ""

#: properties/models.py:175 properties/models.py:267 properties/models.py:363
msgid "Дата на обновяване"
msgstr ""

#: properties/models.py:179 properties/models.py:207 properties/models.py:341
msgid "Имот"
msgstr ""

#: properties/models.py:220
msgid "Алтернативен текст. Важно!"
msgstr ""

#: properties/models.py:222
msgid ""
"Описание на изображението, което се показва при проблем с зареждането му или "
"се използва от хора с увреждания. Попълването на алтернативен текст "
"подобрява достъпността на сайта и оптимизацията за търсачки (SEO)."
msgstr ""

#: properties/models.py:226
msgid "Ширина"
msgstr ""

#: properties/models.py:228
msgid "Височина"
msgstr ""

#: properties/models.py:231
msgid "Размер на файла"
msgstr ""

#: properties/models.py:271
#, fuzzy
#| msgid "Тип имот"
msgid "Снимка на имот"
msgstr "Τύπος Ακινήτου"

#: properties/models.py:272
#, fuzzy
#| msgid "Виж всички имоти"
msgid "Снимки на имоти"
msgstr "Δείτε όλα τα ακίνητα"

#: properties/models.py:343
msgid "YouTube URL"
msgstr ""

#: properties/models.py:345
msgid "Заглавие на видеото"
msgstr ""

#: properties/models.py:347
msgid "Описание на видеото"
msgstr ""

#: properties/models.py:352
msgid "YouTube Video ID"
msgstr ""

#: properties/models.py:355
msgid "URL на thumbnail"
msgstr ""

#: properties/models.py:357
msgid "Embed URL"
msgstr ""

#: properties/models.py:367
#, fuzzy
#| msgid "Тип имот"
msgid "Видео на имот"
msgstr "Τύπος Ακινήτου"

#: properties/models.py:368
#, fuzzy
#| msgid "Виж всички имоти"
msgid "Видеа на имоти"
msgstr "Δείτε όλα τα ακίνητα"
