from django.db import models


class SiteSettings(models.Model):
    # Company
    name = models.CharField(max_length=255, verbose_name="Име на фирмата")
    description = models.TextField(
        verbose_name="Описание на фирмата",
        help_text="Полето се намира най-долу на страницата, под логото.",
    )
    phone = models.CharField(max_length=32, verbose_name="Телефон")
    email = models.EmailField(verbose_name="Email")
    street = models.CharField(max_length=128, verbose_name="Улица")
    city = models.CharField(max_length=128, verbose_name="Град")
    working_hours = models.TextField(verbose_name="Работно време")
    facebook_url = models.URLField(blank=True, null=True, verbose_name="Facebook")
    youtube_url = models.URLField(blank=True, null=True, verbose_name="YouTube")
    instagram_url = models.URLField(blank=True, null=True, verbose_name="Instagram")

    class Meta:
        verbose_name = "Общи настройки"
        verbose_name_plural = "Общи настройки"

    def __str__(self):
        return "Общи настройки"

    @classmethod
    def get_solo(cls):
        return cls.objects.first() or cls.objects.create()
