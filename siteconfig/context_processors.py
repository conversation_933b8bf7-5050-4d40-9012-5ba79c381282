from .models import SiteSettings
from django.core.cache import cache


def site_settings(request):
    settings = cache.get("site_settings")
    if not settings:
        settings = SiteSettings.get_solo()
        cache.set("site_settings", settings, timeout=3600)

    return {
        "COMPANY": {
            "name": settings.name,
            "description": settings.description,
            "phone": settings.phone,
            "email": settings.email,
            "street": settings.street,
            "city": settings.city,
            "working_hours": settings.working_hours,
            "facebook_url": settings.facebook_url,
            "youtube_url": settings.youtube_url,
            "instagram_url": settings.instagram_url,
        }
    }
