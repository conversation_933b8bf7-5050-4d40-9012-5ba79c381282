from django.contrib import admin
from modeltranslation.admin import TabbedTranslationAdmin
from .models import SiteSettings
from .forms import SiteSettingsForm


@admin.register(SiteSettings)
class SiteSettingsAdmin(TabbedTranslationAdmin):
    form = SiteSettingsForm
    fieldsets = (
        (
            "Основни настройки",
            {
                "fields": (
                    "name",
                    "description",
                    "phone",
                    "email",
                    "street",
                    "city",
                )
            },
        ),
        (
            "Работно време",
            {"fields": ("working_hours",)},
        ),
        (
            "Социални мрежи",
            {
                "fields": (
                    "facebook_url",
                    "youtube_url",
                    "instagram_url",
                )
            },
        ),
    )

    def has_add_permission(self, request):
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        return False

    def changelist_view(self, request, extra_context=None):
        obj = SiteSettings.objects.first()
        if obj:
            from django.urls import reverse
            from django.shortcuts import redirect

            return redirect(
                reverse("admin:siteconfig_sitesettings_change", args=[obj.id])
            )
        return super().changelist_view(request, extra_context)
