# Generated by Django 5.2.4 on 2025-07-28 07:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('siteconfig', '0005_sitesettings_youtube_url'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='description',
            field=models.TextField(default='', help_text='Полето се намира най-долу на страницата, под логото.', verbose_name='Описание на фирмата'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='description_bg',
            field=models.TextField(help_text='Полето се намира най-долу на страницата, под логото.', null=True, verbose_name='Описание на фирмата'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='description_el',
            field=models.TextField(help_text='Полето се намира най-долу на страницата, под логото.', null=True, verbose_name='Описание на фирмата'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='description_en',
            field=models.TextField(help_text='Полето се намира най-долу на страницата, под логото.', null=True, verbose_name='Описание на фирмата'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='description_ru',
            field=models.TextField(help_text='Полето се намира най-долу на страницата, под логото.', null=True, verbose_name='Описание на фирмата'),
        ),
    ]
