# Generated by Django 5.2.4 on 2025-07-25 15:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('siteconfig', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='sitesettings',
            name='city',
            field=models.CharField(max_length=128, verbose_name='Град'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='city_bg',
            field=models.CharField(max_length=128, null=True, verbose_name='Град'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='city_el',
            field=models.CharField(max_length=128, null=True, verbose_name='Град'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='city_en',
            field=models.CharField(max_length=128, null=True, verbose_name='Град'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='sitesettings',
            name='city_ru',
            field=models.Char<PERSON>ield(max_length=128, null=True, verbose_name='Град'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='sitesettings',
            name='email',
            field=models.EmailField(max_length=254, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='email_bg',
            field=models.EmailField(max_length=254, null=True, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='email_el',
            field=models.EmailField(max_length=254, null=True, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='email_en',
            field=models.EmailField(max_length=254, null=True, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='email_ru',
            field=models.EmailField(max_length=254, null=True, verbose_name='Email'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='facebook_url',
            field=models.URLField(blank=True, null=True, verbose_name='Facebook'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='instagram_url',
            field=models.URLField(blank=True, null=True, verbose_name='Instagram'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='name',
            field=models.CharField(max_length=255, verbose_name='Име на фирмата'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='name_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Име на фирмата'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='name_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Име на фирмата'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='name_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Име на фирмата'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='name_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Име на фирмата'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='phone',
            field=models.CharField(max_length=32, verbose_name='Телефон'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='phone_bg',
            field=models.CharField(max_length=32, null=True, verbose_name='Телефон'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='phone_el',
            field=models.CharField(max_length=32, null=True, verbose_name='Телефон'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='phone_en',
            field=models.CharField(max_length=32, null=True, verbose_name='Телефон'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='phone_ru',
            field=models.CharField(max_length=32, null=True, verbose_name='Телефон'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='street',
            field=models.CharField(max_length=128, verbose_name='Улица'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='street_bg',
            field=models.CharField(max_length=128, null=True, verbose_name='Улица'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='street_el',
            field=models.CharField(max_length=128, null=True, verbose_name='Улица'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='street_en',
            field=models.CharField(max_length=128, null=True, verbose_name='Улица'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='street_ru',
            field=models.CharField(max_length=128, null=True, verbose_name='Улица'),
        ),
    ]
