# Generated by Django 5.2.4 on 2025-07-25 15:28

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('name_bg', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('name_en', models.<PERSON>r<PERSON><PERSON>(max_length=255, null=True)),
                ('name_el', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('name_ru', models.Char<PERSON>ield(max_length=255, null=True)),
                ('phone', models.Char<PERSON>ield(max_length=32)),
                ('phone_bg', models.<PERSON>r<PERSON><PERSON>(max_length=32, null=True)),
                ('phone_en', models.Char<PERSON><PERSON>(max_length=32, null=True)),
                ('phone_el', models.Char<PERSON><PERSON>(max_length=32, null=True)),
                ('phone_ru', models.Char<PERSON>ield(max_length=32, null=True)),
                ('email', models.EmailField(max_length=254)),
                ('email_bg', models.EmailField(max_length=254, null=True)),
                ('email_en', models.EmailField(max_length=254, null=True)),
                ('email_el', models.EmailField(max_length=254, null=True)),
                ('email_ru', models.EmailField(max_length=254, null=True)),
                ('street', models.TextField()),
                ('street_bg', models.TextField(null=True)),
                ('street_en', models.TextField(null=True)),
                ('street_el', models.TextField(null=True)),
                ('street_ru', models.TextField(null=True)),
                ('city', models.CharField(max_length=255)),
                ('city_bg', models.CharField(max_length=255, null=True)),
                ('city_en', models.CharField(max_length=255, null=True)),
                ('city_el', models.CharField(max_length=255, null=True)),
                ('city_ru', models.CharField(max_length=255, null=True)),
                ('facebook_url', models.URLField(blank=True, null=True)),
                ('instagram_url', models.URLField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Общи настройки',
                'verbose_name_plural': 'Общи настройки',
            },
        ),
    ]
