from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import get_object_or_404, render
from urllib.parse import urlencode

from .models import City, Feature, Property, PropertyType


def property_list_view(request):
    """Страница с всички имоти и филтриране"""
    properties = (
        Property.objects.filter(property_is_active=True)
        .select_related("location", "property_type", "assigned_broker")
        .prefetch_related("features", "images")
    )

    property_type = request.GET.get("property_type")
    location_id = request.GET.get("location")
    min_price = request.GET.get("min_price")
    max_price = request.GET.get("max_price")
    min_area = request.GET.get("min_area")
    max_area = request.GET.get("max_area")
    rooms = request.GET.get("rooms")
    features = request.GET.getlist("features")
    search_query = request.GET.get("search")
    sort_by = request.GET.get("sort_by", "-created_at")

    if property_type:
        properties = properties.filter(property_type__slug=property_type)

    if location_id:
        properties = properties.filter(location__city__id=location_id)

    if min_price:
        properties = properties.filter(price__gte=min_price)

    if max_price:
        properties = properties.filter(price__lte=max_price)

    if min_area:
        properties = properties.filter(area__gte=min_area)

    if max_area:
        properties = properties.filter(area__lte=max_area)

    if rooms:
        properties = properties.filter(rooms=rooms)

    if features:
        for feature_id in features:
            properties = properties.filter(features__id=feature_id)

    if search_query:
        properties = properties.filter(
            Q(title__icontains=search_query)
            | Q(description__icontains=search_query)
            | Q(location__name__icontains=search_query)
            | Q(location__city__name__icontains=search_query)
        )

    if sort_by in ["price", "-price", "area", "-area", "created_at", "-created_at"]:
        properties = properties.order_by(sort_by)

    # Pagination
    page_number = request.GET.get("page", 1)
    paginator = Paginator(properties, 9)  # 9 properties per page (3x3 grid)
    page_obj = paginator.get_page(page_number)

    property_types = PropertyType.objects.all()
    locations = City.objects.all()
    all_features = Feature.objects.all()

    # Build filter query string for pagination URLs
    filter_params = {}
    if property_type:
        filter_params["property_type"] = property_type
    if location_id:
        filter_params["location"] = location_id
    if min_price:
        filter_params["min_price"] = min_price
    if max_price:
        filter_params["max_price"] = max_price
    if min_area:
        filter_params["min_area"] = min_area
    if max_area:
        filter_params["max_area"] = max_area
    if rooms:
        filter_params["rooms"] = rooms
    if features:
        for feature_id in features:
            filter_params[f"features"] = (
                feature_id  # This will only keep the last one, need to handle multiple
            )
    if search_query:
        filter_params["search"] = search_query
    if sort_by != "-created_at":
        filter_params["sort_by"] = sort_by

    # Handle multiple features properly
    filter_query_string = ""
    if filter_params:
        # Handle features separately since they can be multiple
        params_list = []
        for key, value in filter_params.items():
            if key != "features":
                params_list.append(f"{key}={value}")

        # Add features
        for feature_id in features:
            params_list.append(f"features={feature_id}")

        if params_list:
            filter_query_string = "&" + "&".join(params_list)

    context = {
        "properties": page_obj,
        "property_types": property_types,
        "locations": locations,
        "all_features": all_features,
        "total_count": paginator.count,
        "page_obj": page_obj,
        "filter_query_string": filter_query_string,
        "current_filters": {
            "property_type": property_type,
            "location": location_id,
            "min_price": min_price,
            "max_price": max_price,
            "min_area": min_area,
            "max_area": max_area,
            "rooms": rooms,
            "features": features,
            "search": search_query,
            "sort_by": sort_by,
        },
    }

    if request.headers.get("HX-Request"):
        response = render(request, "partials/property_list_results.html", context)
        response["X-Total-Count"] = str(context["total_count"])
        return response

    return render(request, "core/property_list.html", context)


def property_detail_view(request, slug):
    """Детайлна страница за конкретен имот"""
    property = get_object_or_404(
        Property.objects.select_related(
            "location", "property_type", "assigned_broker"
        ).prefetch_related("features", "images", "videos"),
        slug=slug,
        property_is_active=True,
    )

    similar_properties = (
        Property.objects.filter(
            property_type=property.property_type,
            location=property.location,
            property_is_active=True,
        )
        .exclude(id=property.id)
        .select_related("location", "property_type")[:4]
    )

    images = property.images.all()
    videos = property.videos.all()

    # Prepare unified media items (images and videos combined)
    media_items = []

    # Add images
    for image in images:
        media_items.append(
            {
                "type": "image",
                "order": image.order,
                "url": image.image.url,
                "thumbnail_url": image.thumbnail.url,
                "alt_text": image.alt_text,
            }
        )

    # Add videos
    from .utils import get_youtube_thumbnail_url

    for video in videos:
        # Determine a reliable thumbnail URL
        thumb = video.thumbnail_url or get_youtube_thumbnail_url(
            video.video_id, "hqdefault"
        )
        if thumb and "maxresdefault" in thumb:
            thumb = thumb.replace("maxresdefault", "hqdefault")
        media_items.append(
            {
                "type": "video",
                "order": video.order,
                "embed_url": video.embed_url,
                "thumbnail_url": thumb,
                "title": video.title,
            }
        )

    # Sort by order field
    media_items.sort(key=lambda x: x["order"])

    # JSON encode for safe JavaScript consumption
    import json

    media_items_json = json.dumps(media_items)

    context = {
        "property": property,
        "similar_properties": similar_properties,
        "images": images,
        "videos": videos,
        "media_items": media_items,
        "media_items_json": media_items_json,
    }
    return render(request, "core/property_detail.html", context)


# Create your views here.
