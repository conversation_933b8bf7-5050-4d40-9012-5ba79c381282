"""
Конфигурация за многоезичност с django-modeltranslation за приложението 'properties'
"""

from modeltranslation.translator import register, TranslationOptions
from .models import (
    Property,
    PropertyType,
    Feature,
    City,
    Location,
    Badge,
    PropertyImage,
)


@register(City)
class CityTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(Location)
class LocationTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(PropertyType)
class PropertyTypeTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(Feature)
class FeatureTranslationOptions(TranslationOptions):
    fields = ("name",)


@register(Property)
class PropertyTranslationOptions(TranslationOptions):
    fields = (
        "title",
        "description",
    )


@register(Badge)
class BadgeTranslationOptions(TranslationOptions):
    fields = ("badge",)


@register(PropertyImage)
class PropertyImageTranslationOptions(TranslationOptions):
    fields = ("alt_text",)
