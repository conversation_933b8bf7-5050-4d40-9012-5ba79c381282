import logging
import os
import re
import uuid
from io import BytesIO

from django.core.files.base import ContentFile
from PIL import Image, ExifTags

# Get an instance of a logger
logger = logging.getLogger(__name__)


def fix_image_orientation(image):
    try:
        for orientation in ExifTags.TAGS.keys():
            if ExifTags.TAGS[orientation] == "Orientation":
                break

        exif = dict(image._getexif().items())

        if exif[orientation] == 3:
            image = image.rotate(180, expand=True)
        elif exif[orientation] == 6:
            image = image.rotate(270, expand=True)
        elif exif[orientation] == 8:
            image = image.rotate(90, expand=True)

    except (AttributeError, KeyError, IndexError):
        # cases: image don't have getexif
        pass
    return image


def optimize_image(
    image_file, max_width=1920, max_height=1080, quality=85, format="JPEG"
):
    """
    Оптимизира изображение - преоразмерява, компресира и коригира ориентацията

    Args:
        image_file: Django UploadedFile или PIL Image
        max_width: Максимална ширина в пиксели
        max_height: Максимална височина в пиксели
        quality: Качество на JPEG компресията (1-100)
        format: Формат на изходното изображение

    Returns:
        ContentFile: Оптимизираното изображение като Django ContentFile
    """
    try:
        # Отваряме изображението
        if hasattr(image_file, "read"):
            image = Image.open(image_file)
        else:
            image = image_file

        # Коригираме ориентацията според EXIF данните
        image = fix_image_orientation(image)

        # Конвертираме в RGB ако е необходимо (за JPEG)
        if format.upper() == "JPEG" and image.mode in ("RGBA", "LA", "P"):
            # Създаваме бял фон за прозрачни изображения
            background = Image.new("RGB", image.size, (255, 255, 255))
            if image.mode == "P":
                image = image.convert("RGBA")
            background.paste(
                image, mask=image.split()[-1] if image.mode == "RGBA" else None
            )
            image = background
        elif format.upper() == "PNG" and image.mode not in ("RGBA", "RGB", "L"):
            image = image.convert("RGBA")

        # Преоразмеряваме изображението ако е необходимо
        if image.width > max_width or image.height > max_height:
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

        # Записваме оптимизираното изображение в BytesIO
        output = BytesIO()

        # Настройки за запазване според формата
        save_kwargs = {}
        if format.upper() == "JPEG":
            save_kwargs.update(
                {"quality": quality, "optimize": True, "progressive": True}
            )
        elif format.upper() == "PNG":
            save_kwargs.update({"optimize": True, "compress_level": 6})

        image.save(output, format=format, **save_kwargs)
        output.seek(0)

        # Връщаме като ContentFile
        return ContentFile(output.getvalue())

    except Exception as e:
        logger.error(f"Грешка при оптимизация на изображение: {e}")
        raise


def validate_image_file(uploaded_file):
    """
    Валидира качения файл с изображение

    Args:
        uploaded_file: Django UploadedFile или ImageFieldFile

    Returns:
        tuple: (is_valid, error_message)
    """
    # Проверяваме дали файлът съществува
    if not uploaded_file:
        return False, "Няма избран файл."

    # Проверяваме дали това е нов файл (UploadedFile) или съществуващ (ImageFieldFile)
    is_uploaded_file = hasattr(uploaded_file, "content_type")

    # Проверяваме размера на файла (максимум 10MB) - само за нови файлове
    if is_uploaded_file:
        max_size = 10 * 1024 * 1024  # 10MB
        if uploaded_file.size > max_size:
            return False, "Файлът е твърде голям. Максималният размер е 10MB."

        # Проверяваме типа на файла
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
        if uploaded_file.content_type not in allowed_types:
            return False, "Неподдържан формат на файла. Разрешени са: JPEG, PNG, WebP."

    # Проверяваме разширението на файла
    allowed_extensions = [".jpg", ".jpeg", ".png", ".webp"]
    file_extension = os.path.splitext(uploaded_file.name)[1].lower()
    if file_extension not in allowed_extensions:
        return False, "Неподдържано разширение на файла."

    try:
        # Опитваме се да отворим файла като изображение
        image = Image.open(uploaded_file)
        image.verify()  # Проверяваме дали файлът е валидно изображение

        # Проверяваме размерите на изображението
        # За ImageFieldFile трябва да отворим файла отново след verify()
        if hasattr(uploaded_file, "seek"):
            uploaded_file.seek(0)  # Връщаме указателя в началото
        image = Image.open(uploaded_file)
        width, height = image.size

        # Минимални размери - само за нови файлове
        if is_uploaded_file and (width < 300 or height < 200):
            return (
                False,
                "Изображението е твърде малко. Минималните размери са 300x200 пиксела.",
            )

        # Максимални размери - само за нови файлове
        if is_uploaded_file and (width > 5000 or height > 5000):
            return (
                False,
                "Изображението е твърде голямо. Максималните размери са 5000x5000 пиксела.",
            )

    except Exception as e:
        return False, f"Файлът не е валидно изображение: {str(e)}"

    return True, ""


def extract_youtube_video_id(url):
    """
    Извлича YouTube video ID от различни формати на URL

    Args:
        url: YouTube URL в различни формати

    Returns:
        str: Video ID или None ако URL не е валиден
    """
    if not url:
        return None

    # Различни формати на YouTube URL
    patterns = [
        r"(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})",
        r"youtube\.com\/v\/([a-zA-Z0-9_-]{11})",
        r"youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})",
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return None


def validate_youtube_url(url):
    """
    Валидира дали URL е валиден YouTube линк

    Args:
        url: URL за проверка

    Returns:
        tuple: (is_valid, error_message)
    """
    if not url:
        return False, "URL е задължителен"

    video_id = extract_youtube_video_id(url)
    if not video_id:
        return False, "Невалиден YouTube URL. Моля използвайте валиден YouTube линк."

    # Проверяваме дали video ID е с правилна дължина
    if len(video_id) != 11:
        return False, "Невалиден YouTube video ID"

    return True, ""


def get_youtube_thumbnail_url(video_id, quality="hqdefault"):
    """
    Генерира URL за thumbnail на YouTube видео

    Args:
        video_id: YouTube video ID
        quality: Качество на thumbnail ('maxresdefault', 'hqdefault', 'mqdefault', 'sddefault')

    Returns:
        str: URL на thumbnail
    """
    if not video_id:
        return ""

    return f"https://img.youtube.com/vi/{video_id}/{quality}.jpg"


def get_youtube_embed_url(video_id):
    """
    Генерира embed URL за YouTube видео

    Args:
        video_id: YouTube video ID

    Returns:
        str: Embed URL
    """
    if not video_id:
        return ""

    return f"https://www.youtube.com/embed/{video_id}"
