from django import forms
from django.conf import settings
from django.core.exceptions import ValidationError

from .models import PropertyImage
from .utils import validate_image_file


class PropertyImageForm(forms.ModelForm):
    """Форма за качване на снимки на имоти с разширена валидация"""

    class Meta:
        model = PropertyImage
        fields = ["image", "order", "alt_text"]
        widgets = {
            "alt_text": forms.TextInput(
                attrs={
                    "placeholder": "Алтернативен текст за изображението (по избор)",
                    "class": "form-control",
                }
            ),
            "order": forms.NumberInput(attrs={"min": 1, "class": "form-control"}),
        }

    def clean_image(self):
        """Разширена валидация на качения файл с изображение"""
        image = self.cleaned_data.get("image")

        if not image:
            return image

        is_valid, error_message = validate_image_file(image)
        if not is_valid:
            raise ValidationError(error_message)

        try:
            import magic

            mime_type = magic.from_buffer(image.read(1024), mime=True)
            image.seek(0)

            if mime_type not in settings.ALLOWED_IMAGE_TYPES:
                raise ValidationError(
                    f"Неподдържан MIME тип: {mime_type}. "
                    f"Разрешени са: {', '.join(settings.ALLOWED_IMAGE_TYPES)}"
                )
        except ImportError:
            pass
        except Exception as e:
            raise ValidationError(f"Грешка при проверка на файла: {str(e)}")

        return image

    def clean_order(self):
        """Валидация на поредността на снимката"""
        order = self.cleaned_data.get("order")
        property = self.cleaned_data.get("property")

        if order and property:
            existing = PropertyImage.objects.filter(
                property=property, order=order
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing.exists():
                raise ValidationError(
                    f"Вече съществува снимка с поредност {order} за този имот."
                )

        return order


class PropertyImageInlineFormSet(forms.BaseInlineFormSet):
    """Формсет за inline управление на снимки в админ панела"""

    def clean(self):
        """Валидация на целия формсет"""
        if any(self.errors):
            return

        orders = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get("DELETE", False):
                order = form.cleaned_data.get("order")
                if order:
                    if order in orders:
                        raise ValidationError(
                            "Не може да има две снимки с еднаква поредност."
                        )
                    orders.append(order)

        if orders and 1 not in orders:
            raise ValidationError(
                "Трябва да има поне една снимка с поредност 1 (главна снимка)."
            )


class BulkImageUploadForm(forms.Form):
    """Форма за масово качване на снимки"""

    image = forms.ImageField(
        help_text="Изберете изображение за качване.",
    )

    def clean_image(self):
        """Валидация на файла"""
        image = self.cleaned_data.get("image")

        if image:
            is_valid, error_message = validate_image_file(image)
            if not is_valid:
                raise ValidationError(error_message)

        return image
