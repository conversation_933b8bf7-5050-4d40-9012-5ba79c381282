from django.contrib import admin
from django.utils.html import format_html
from modeltranslation.admin import (
    TabbedTranslationAdmin,
    TranslationTabularInline,
)
from adminsortable2.admin import SortableAdminMixin

from core.admin_utils import info_message


from .models import (
    City,
    Feature,
    Location,
    Badge,
    Property,
    PropertyImage,
    PropertyType,
    PropertyVideo,
)


class PropertyImageInline(TranslationTabularInline):
    """Inline админ за снимки на имоти с подобрено управление"""

    model = PropertyImage
    extra = 0
    ordering = ("order",)
    fields = ("image", "order", "alt_text", "image_preview", "image_info")
    readonly_fields = (
        "image_preview",
        "image_info",
    )

    def image_preview(self, obj):
        """Показва миниатюра на изображението"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px; object-fit: cover;" />',
                obj.thumbnail.url if hasattr(obj, "thumbnail") else obj.image.url,
            )
        return "Няма изображение"

    image_preview.short_description = "Преглед"

    def image_info(self, obj):
        """Показва информация за изображението"""
        if obj.image:
            info_parts = []
            if obj.width and obj.height:
                info_parts.append(f"{obj.width}x{obj.height}px")
            if obj.file_size:
                info_parts.append(obj.file_size_formatted)
            return " | ".join(info_parts) if info_parts else "Няма данни"
        return "-"

    image_info.short_description = "Информация"


class PropertyVideoInline(admin.TabularInline):
    """Inline админ за видеа на имоти"""

    model = PropertyVideo
    extra = 0
    ordering = ("order",)
    fields = ("youtube_url", "title", "order", "video_preview", "video_info")
    readonly_fields = ("video_preview", "video_info")

    def video_preview(self, obj):
        """Показва thumbnail на видеото"""
        from .utils import get_youtube_thumbnail_url

        thumbnail = obj.thumbnail_url
        # Fallback to hqdefault if maxresdefault is unavailable
        if thumbnail and "maxresdefault" in thumbnail:
            thumbnail = thumbnail.replace("maxresdefault", "hqdefault")
        if not thumbnail and obj.video_id:
            thumbnail = get_youtube_thumbnail_url(obj.video_id, "hqdefault")

        if thumbnail:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 75px; object-fit: cover;" />',
                thumbnail,
            )
        return "Няма thumbnail"

    video_preview.short_description = "Преглед"

    def video_info(self, obj):
        """Показва информация за видеото"""
        if obj.video_id:
            info_parts = [f"ID: {obj.video_id}"]
            if obj.title:
                info_parts.append(f"Заглавие: {obj.title}")
            return " | ".join(info_parts)
        return "Няма данни"

    video_info.short_description = "Информация"


@admin.register(Property)
class PropertyAdmin(SortableAdminMixin, TabbedTranslationAdmin):
    list_display = (
        "title",
        "location",
        "badge",
        "property_type",
        "price",
        "currency",
        "area",
        "rooms",
        "property_is_active",
        "is_featured",
    )
    list_filter = (
        "property_type",
        "location",
        "property_is_active",
        "is_featured",
        "assigned_broker",
    )
    search_fields = ("title", "description", "location__name")
    filter_horizontal = ("features",)
    inlines = [PropertyImageInline, PropertyVideoInline]
    list_editable = ("property_is_active", "is_featured")

    # Ensure proper ordering for sortable functionality
    ordering = ["order"]

    # Add explicit Media class to ensure JavaScript is loaded
    class Media:
        js = ()  # Let SortableAdminMixin handle the JS
        css = {}

    fieldsets = (
        (
            ("Настройки за публикуване"),
            {"fields": ("property_is_active", "is_featured")},
        ),
        (
            ("Основна информация"),
            {"fields": ("title", "property_type", "location", "badge")},
        ),
        (
            ("Детайли за имота"),
            {"fields": ("price", "currency", "area", "rooms", "description")},
        ),
        (("Екстри и брокер"), {"fields": ("features", "assigned_broker")}),
    )


@admin.register(Location)
class LocationAdmin(TabbedTranslationAdmin):
    list_display = ("city", "name")
    list_filter = ("city",)
    search_fields = ("name",)
    ordering = ("city", "name")
    fields = ("city", "name", "show_info", "google_map_iframe")
    readonly_fields = ("show_info",)

    def show_info(self, obj):
        return info_message(
            """
            <p><b>Как да добавим карта от Google Maps?</b></p>
            <ol style="margin-left: 0px;">
                <li>Посетете <a href="https://www.google.bg/maps" target="_blank">Google Maps</a></li>
                <li>Намерете желаната локация</li>
                <li>Кликнете на "Споделяне" (Share)</li>
                <li>Кликнете на "Вграждане на карта" (Embed a map)</li>
                <li>Копирайте кода, който започва с "iframe src=...", като натиснете на бутона "КОПИРАНЕ НА HTML КОДА" (COPY HTML)</li>
                <li>Поставете копирания код в полето по-долу (Google Map iframe)</li>
            </ol>
            """
        )

    show_info.short_description = "Упътване за добавяне на карта от Google Maps"


@admin.register(City)
class CityAdmin(TabbedTranslationAdmin):
    list_display = ("name",)
    list_filter = ("name",)
    search_fields = ("name",)
    ordering = ("name",)


@admin.register(Badge)
class BadgeAdmin(TabbedTranslationAdmin):
    list_display = ("badge",)
    search_fields = ("badge",)


@admin.register(PropertyType)
class PropertyTypeAdmin(TabbedTranslationAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Feature)
class FeatureAdmin(TabbedTranslationAdmin):
    list_display = ("name",)
    search_fields = ("name",)


# Register your models here.
