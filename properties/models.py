from locale import currency
from django.db import models
from django.urls import reverse
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from autoslug import AutoSlugField
from imagekit.models import ImageSpecField
from imagekit.processors import ResizeToFit, Thumbnail

from .utils import (
    validate_image_file,
    optimize_image,
    validate_youtube_url,
    extract_youtube_video_id,
    get_youtube_thumbnail_url,
    get_youtube_embed_url,
)


def get_property_image_upload_path(instance, filename):
    import uuid

    """
    Генерира пътя за качване на снимки на имоти с подобрена структура.
    Дефинирана е тук, за да се избегнат циклични импорти при стартиране на Django.
    """
    ext = filename.split(".")[-1].lower()
    unique_filename = f"{uuid.uuid4().hex}.{ext}"
    # Използваме instance.property_id вместо instance.property.id, за да избегнем
    # потенциални проблеми при зареждане на свързания обект по време на инициализация.
    return f"property_images/{instance.listing_id}/{unique_filename}"


class City(models.Model):
    """Модел за градове"""

    name = models.CharField(max_length=100, verbose_name="Име на града")

    class Meta:
        verbose_name = "Град"
        verbose_name_plural = "Градове"
        ordering = ["name"]

    def __str__(self):
        return self.name


class Location(models.Model):
    """Модел за локации (градове, квартали) с йерархична структура"""

    name = models.CharField(max_length=100, verbose_name="Име на локацията")
    city = models.ForeignKey(
        City, on_delete=models.CASCADE, null=True, blank=True, verbose_name="Град"
    )
    google_map_url = models.URLField(
        max_length=1024,
        verbose_name="URL на Google Map",
        blank=True,
        null=True,
    )
    google_map_iframe = models.TextField(
        verbose_name="Google Map iframe",
        blank=True,
        null=True,
        help_text="Поставете тук пълният iframe код от Google Maps. Данните ще се извлекат автоматично.",
    )

    class Meta:
        verbose_name = "Адрес"
        verbose_name_plural = "Адреси"
        ordering = ["city", "name"]

    @staticmethod
    def extract_src(iframe_html: str) -> str | None:
        if not iframe_html:
            return None
        start = iframe_html.find('src="')
        if start == -1:
            return None
        start += len('src="')
        end = iframe_html.find('"', start)
        return iframe_html[start:end] if end != -1 else None

    def save(self, *args, **kwargs):
        self.google_map_url = self.extract_src(self.google_map_iframe)
        super().save(*args, **kwargs)

    def __str__(self):
        if self.city:
            return f"{self.name}, {self.city.name}"
        return self.name


class PropertyType(models.Model):
    """Модел за типовете имоти (Апартамент, Къща, и др.)"""

    name = models.CharField(max_length=50, verbose_name="Име на типа")
    slug = AutoSlugField(
        populate_from="name",
        unique=True,
        always_update=False,
        max_length=50,
        verbose_name="URL адрес",
    )

    class Meta:
        verbose_name = "Тип имот"
        verbose_name_plural = "Типове имоти"
        ordering = ["name"]

    def __str__(self):
        return self.name


class Feature(models.Model):
    """Модел за екстрите на имотите (Гараж, Асансьор, и др.)"""

    name = models.CharField(max_length=50, verbose_name="Име на екстра")

    class Meta:
        verbose_name = "Екстра"
        verbose_name_plural = "Екстри"
        ordering = ["name"]

    def __str__(self):
        return self.name


class Badge(models.Model):
    badge = models.CharField(
        max_length=50,
        verbose_name="Значка",
        default="",
        null=True,
        blank=True,
        help_text='"Значка" за обява, която се показва в левия ъгъл на снимката на обявата. Например: "Топ оферта", "Премиум" и др.',
    )

    class Meta:
        verbose_name = "Значка"
        verbose_name_plural = "Значки"
        ordering = ["badge"]

    def __str__(self):
        return self.badge


class Property(models.Model):
    """Основният модел за обявите за имоти"""

    title = models.CharField(max_length=200, verbose_name="Заглавие на обявата")
    slug = AutoSlugField(
        populate_from="title",
        unique=True,
        always_update=False,
        max_length=200,
        verbose_name="URL адрес",
    )
    property_type = models.ForeignKey(
        PropertyType, on_delete=models.CASCADE, verbose_name="Тип имот"
    )

    location = models.ForeignKey(
        Location, on_delete=models.CASCADE, verbose_name="Адрес"
    )
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Цена")
    currency = models.CharField(
        max_length=3,
        choices=(
            ("€", "EUR"),
            ("лв.", "BGN"),
        ),
        default="€",
        verbose_name="Валута",
    )
    area = models.IntegerField(verbose_name="Площ")
    rooms = models.PositiveIntegerField(verbose_name="Брой стаи", default=0)
    description = models.TextField(verbose_name="Пълно описание")
    features = models.ManyToManyField(Feature, blank=True, verbose_name="Екстри")
    assigned_broker = models.ForeignKey(
        "core.TeamMember",
        on_delete=models.CASCADE,
        verbose_name="Отговорен брокер",
        related_name="properties",
    )
    property_is_active = models.BooleanField(
        default=True,
        verbose_name="Активна обява",
        help_text="Тук можете да деактивирате обявата, без да я изтривате и по-късно да я активирате отново.",
    )
    is_featured = models.BooleanField(
        default=False,
        verbose_name="Топ оферта",
        help_text='Този имот ще бъде видим на главната страница, в секцията "Препоръчани имоти". Внимание: В Секцията "Препоръчани имоти" се показват максимално 6 имота',
    )
    badge = models.ForeignKey(
        Badge,
        on_delete=models.CASCADE,
        verbose_name="Значка",
        null=True,
        blank=True,
    )
    order = models.PositiveSmallIntegerField(default=0, blank=False, null=False)

    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата на създаване"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Дата на обновяване")

    class Meta:
        verbose_name = "Имот"
        verbose_name_plural = "Имоти"
        ordering = ["order"]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse("properties:property-detail", kwargs={"slug": self.slug})

    @property
    def main_image(self):
        """Връща главната снимка на имота (първата в поредността)"""
        return self.images.first()


# ==============================================================================
# Property Media Models
# ==============================================================================


class PropertyImage(models.Model):
    """Модел за галерията със снимки към всеки имот с оптимизация"""

    listing = models.ForeignKey(
        "Property",
        on_delete=models.CASCADE,
        related_name="images",
        verbose_name="Имот",
    )
    image = models.ImageField(
        upload_to=get_property_image_upload_path, verbose_name="Снимка"
    )
    order = models.PositiveIntegerField(
        default=0, blank=False, null=False, verbose_name="Поредност"
    )

    # Метаданни за изображението
    alt_text = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="Алтернативен текст. Важно!",
        help_text="Описание на изображението, което се показва при проблем с зареждането му или се използва от хора с увреждания. "
        "Попълването на алтернативен текст подобрява достъпността на сайта и оптимизацията за търсачки (SEO).",
    )
    width = models.PositiveIntegerField(null=True, blank=True, verbose_name="Ширина")
    height = models.PositiveIntegerField(null=True, blank=True, verbose_name="Височина")
    file_size = models.PositiveIntegerField(
        null=True, blank=True, verbose_name="Размер на файла"
    )

    # Автоматично генерирани варианти на изображението
    thumbnail = ImageSpecField(
        source="image",
        processors=[ResizeToFit(120, 120)],
        format="JPEG",
        options={"quality": 80},
    )

    small = ImageSpecField(
        source="image",
        processors=[ResizeToFit(400)],
        format="JPEG",
        options={"quality": 85},
    )

    medium = ImageSpecField(
        source="image",
        processors=[ResizeToFit(800, 600)],
        format="JPEG",
        options={"quality": 85},
    )

    large = ImageSpecField(
        source="image",
        processors=[ResizeToFit(1920, 1080)],
        format="JPEG",
        options={"quality": 90},
    )

    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата на създаване"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Дата на обновяване")

    class Meta:
        verbose_name = "Снимка на имот"
        verbose_name_plural = "Снимки на имоти"
        ordering = ["order"]

    def __str__(self):
        return f"Снимка {self.order} - {self.listing.title}"

    def clean(self):
        """Валидация на качения файл"""
        if self.image:
            is_valid, error_message = validate_image_file(self.image)
            if not is_valid:
                raise ValidationError({"image": error_message})

    def save(self, *args, **kwargs):
        """Преработваме save метода за оптимизация на изображението"""
        # Проверяваме дали това е нов файл или съществуващ
        is_new_image = self.pk is None or (
            self.pk and hasattr(self.image, "content_type")
        )

        if self.image and is_new_image:
            # Оптимизираме изображението преди запазване - само за нови файлове
            optimized_image = optimize_image(self.image, max_width=800, max_height=600)

            # Запазваме оптимизираното изображение
            self.image.save(self.image.name, optimized_image, save=False)

        # Извличаме/обновяваме метаданни
        if self.image:
            from PIL import Image

            img = Image.open(self.image)
            self.width = img.width
            self.height = img.height
            self.file_size = self.image.size

            # Генерираме alt текст ако не е зададен
            if not self.alt_text:
                self.alt_text = f"Снимка {self.order} от {self.listing.title}"

        super().save(*args, **kwargs)

    @property
    def aspect_ratio(self):
        """Връща съотношението ширина/височина"""
        if self.width and self.height:
            return round(self.width / self.height, 2)
        return None

    @property
    def file_size_formatted(self):
        """Връща форматирания размер на файла"""
        if self.file_size:
            if self.file_size < 1024:
                return f"{self.file_size} B"
            elif self.file_size < 1024 * 1024:
                return f"{round(self.file_size / 1024, 1)} KB"
            else:
                return f"{round(self.file_size / (1024 * 1024), 1)} MB"
        return "Неизвестен"


class PropertyVideo(models.Model):
    """Модел за YouTube видеа към всеки имот"""

    listing = models.ForeignKey(
        "Property",
        on_delete=models.CASCADE,
        related_name="videos",
        verbose_name="Имот",
    )
    youtube_url = models.URLField(max_length=500, verbose_name="YouTube URL")
    title = models.CharField(
        max_length=200, blank=True, verbose_name="Заглавие на видеото"
    )
    description = models.TextField(blank=True, verbose_name="Описание на видеото")
    order = models.PositiveIntegerField(default=0, verbose_name="Поредност")

    # Автоматично генерирани полета
    video_id = models.CharField(
        max_length=20, blank=True, verbose_name="YouTube Video ID"
    )
    thumbnail_url = models.URLField(
        max_length=500, blank=True, verbose_name="URL на thumbnail"
    )
    embed_url = models.URLField(max_length=500, blank=True, verbose_name="Embed URL")

    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата на създаване"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Дата на обновяване")

    class Meta:
        verbose_name = "Видео на имот"
        verbose_name_plural = "Видеа на имоти"
        ordering = ["order"]
        unique_together = ("listing", "order")

    def __str__(self):
        title = self.title or "Видео"
        if self.listing:
            return f"{title} - {self.listing.title}"

    def clean(self):
        """Валидация на YouTube URL"""
        if self.youtube_url:
            is_valid, error_message = validate_youtube_url(self.youtube_url)
            if not is_valid:
                raise ValidationError({"youtube_url": error_message})

    def save(self, *args, **kwargs):
        """Автоматично извличане на video ID и генериране на URLs"""
        if self.youtube_url:
            # Извличаме video ID
            self.video_id = extract_youtube_video_id(self.youtube_url)

            if self.video_id:
                # Генерираме thumbnail и embed URLs
                self.thumbnail_url = get_youtube_thumbnail_url(self.video_id)
                self.embed_url = get_youtube_embed_url(self.video_id)

                # Ако няма заглавие, използваме стандартно
                if not self.title:
                    self.title = f"Видео {self.order}"

        super().save(*args, **kwargs)

    @property
    def is_valid_video(self):
        """Проверява дали видеото има валиден video ID"""
        return bool(self.video_id)
