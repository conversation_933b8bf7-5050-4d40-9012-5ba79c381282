# Generated by Django 5.2.4 on 2025-07-26 16:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0006_propertyimage_alt_text_bg_propertyimage_alt_text_el_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='feature',
            options={'ordering': ['name'], 'verbose_name': 'Екстра', 'verbose_name_plural': 'Екстри'},
        ),
        migrations.RemoveField(
            model_name='property',
            name='bedrooms',
        ),
        migrations.AddField(
            model_name='property',
            name='rooms',
            field=models.PositiveIntegerField(default=0, verbose_name='Брой стаи'),
        ),
        migrations.AlterField(
            model_name='feature',
            name='name',
            field=models.CharField(max_length=50, verbose_name='Име на екстра'),
        ),
        migrations.AlterField(
            model_name='feature',
            name='name_bg',
            field=models.Char<PERSON>ield(max_length=50, null=True, verbose_name='Име на екстра'),
        ),
        migrations.AlterField(
            model_name='feature',
            name='name_el',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на екстра'),
        ),
        migrations.AlterField(
            model_name='feature',
            name='name_en',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на екстра'),
        ),
        migrations.AlterField(
            model_name='feature',
            name='name_ru',
            field=models.CharField(max_length=50, null=True, verbose_name='Име на екстра'),
        ),
        migrations.AlterField(
            model_name='property',
            name='features',
            field=models.ManyToManyField(blank=True, to='properties.feature', verbose_name='Екстри'),
        ),
        migrations.AlterField(
            model_name='propertyimage',
            name='alt_text',
            field=models.CharField(blank=True, help_text='Описание на изображението, което се показва при проблем с зареждането му или се използва от хора с увреждания. Попълването на алтернативен текст подобрява достъпността на сайта и оптимизацията за търсачки (SEO).', max_length=255, verbose_name='Алтернативен текст. Важно!'),
        ),
        migrations.AlterField(
            model_name='propertyimage',
            name='alt_text_bg',
            field=models.CharField(blank=True, help_text='Описание на изображението, което се показва при проблем с зареждането му или се използва от хора с увреждания. Попълването на алтернативен текст подобрява достъпността на сайта и оптимизацията за търсачки (SEO).', max_length=255, null=True, verbose_name='Алтернативен текст. Важно!'),
        ),
        migrations.AlterField(
            model_name='propertyimage',
            name='alt_text_el',
            field=models.CharField(blank=True, help_text='Описание на изображението, което се показва при проблем с зареждането му или се използва от хора с увреждания. Попълването на алтернативен текст подобрява достъпността на сайта и оптимизацията за търсачки (SEO).', max_length=255, null=True, verbose_name='Алтернативен текст. Важно!'),
        ),
        migrations.AlterField(
            model_name='propertyimage',
            name='alt_text_en',
            field=models.CharField(blank=True, help_text='Описание на изображението, което се показва при проблем с зареждането му или се използва от хора с увреждания. Попълването на алтернативен текст подобрява достъпността на сайта и оптимизацията за търсачки (SEO).', max_length=255, null=True, verbose_name='Алтернативен текст. Важно!'),
        ),
        migrations.AlterField(
            model_name='propertyimage',
            name='alt_text_ru',
            field=models.CharField(blank=True, help_text='Описание на изображението, което се показва при проблем с зареждането му или се използва от хора с увреждания. Попълването на алтернативен текст подобрява достъпността на сайта и оптимизацията за търсачки (SEO).', max_length=255, null=True, verbose_name='Алтернативен текст. Важно!'),
        ),
    ]
