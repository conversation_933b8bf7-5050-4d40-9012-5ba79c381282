# Generated by Django 5.2.4 on 2025-07-31 09:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0016_property_badge'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='badge',
            options={'ordering': ['badge'], 'verbose_name': 'Значка', 'verbose_name_plural': 'Значки'},
        ),
        migrations.AddField(
            model_name='badge',
            name='badge_bg',
            field=models.CharField(blank=True, default='', help_text='"Значка" за обява, която се показва в левия ъгъл на снимката на обявата. Например: "Топ оферта", "Премиум" и др.', max_length=50, null=True, verbose_name='Значка'),
        ),
        migrations.AddField(
            model_name='badge',
            name='badge_el',
            field=models.CharField(blank=True, default='', help_text='"Значка" за обява, която се показва в левия ъгъл на снимката на обявата. Например: "Топ оферта", "Премиум" и др.', max_length=50, null=True, verbose_name='Значка'),
        ),
        migrations.AddField(
            model_name='badge',
            name='badge_en',
            field=models.CharField(blank=True, default='', help_text='"Значка" за обява, която се показва в левия ъгъл на снимката на обявата. Например: "Топ оферта", "Премиум" и др.', max_length=50, null=True, verbose_name='Значка'),
        ),
        migrations.AddField(
            model_name='badge',
            name='badge_ru',
            field=models.CharField(blank=True, default='', help_text='"Значка" за обява, която се показва в левия ъгъл на снимката на обявата. Например: "Топ оферта", "Премиум" и др.', max_length=50, null=True, verbose_name='Значка'),
        ),
    ]
