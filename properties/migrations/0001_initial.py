# Generated by Django 5.2.4 on 2025-07-25 17:09

import autoslug.fields
import django.db.models.deletion
import properties.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име на града')),
                ('name_bg', models.Char<PERSON>ield(max_length=100, null=True, verbose_name='Име на града')),
                ('name_en', models.CharField(max_length=100, null=True, verbose_name='Име на града')),
                ('name_el', models.CharField(max_length=100, null=True, verbose_name='Име на града')),
                ('name_ru', models.Char<PERSON>ield(max_length=100, null=True, verbose_name='Име на града')),
            ],
            options={
                'verbose_name': 'Град',
                'verbose_name_plural': 'Градове',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='Име на особеността')),
                ('name_bg', models.CharField(max_length=50, null=True, verbose_name='Име на особеността')),
                ('name_en', models.CharField(max_length=50, null=True, verbose_name='Име на особеността')),
                ('name_el', models.CharField(max_length=50, null=True, verbose_name='Име на особеността')),
                ('name_ru', models.CharField(max_length=50, null=True, verbose_name='Име на особеността')),
            ],
            options={
                'verbose_name': 'Особеност',
                'verbose_name_plural': 'Особености',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PropertyType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='Име на типа')),
                ('name_bg', models.CharField(max_length=50, null=True, verbose_name='Име на типа')),
                ('name_en', models.CharField(max_length=50, null=True, verbose_name='Име на типа')),
                ('name_el', models.CharField(max_length=50, null=True, verbose_name='Име на типа')),
                ('name_ru', models.CharField(max_length=50, null=True, verbose_name='Име на типа')),
                ('slug', autoslug.fields.AutoSlugField(editable=False, populate_from='name', unique=True, verbose_name='URL адрес')),
            ],
            options={
                'verbose_name': 'Тип имот',
                'verbose_name_plural': 'Типове имоти',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име на локацията')),
                ('name_bg', models.CharField(max_length=100, null=True, verbose_name='Име на локацията')),
                ('name_en', models.CharField(max_length=100, null=True, verbose_name='Име на локацията')),
                ('name_el', models.CharField(max_length=100, null=True, verbose_name='Име на локацията')),
                ('name_ru', models.CharField(max_length=100, null=True, verbose_name='Име на локацията')),
                ('city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='properties.city', verbose_name='Град')),
            ],
            options={
                'verbose_name': 'Локация',
                'verbose_name_plural': 'Локации',
                'ordering': ['city', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Заглавие на обявата')),
                ('title_bg', models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата')),
                ('title_en', models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата')),
                ('title_el', models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата')),
                ('title_ru', models.CharField(max_length=200, null=True, verbose_name='Заглавие на обявата')),
                ('slug', autoslug.fields.AutoSlugField(editable=False, max_length=200, populate_from='title', unique=True, verbose_name='URL адрес')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Цена')),
                ('area', models.IntegerField(verbose_name='Площ (кв.м.)')),
                ('bedrooms', models.PositiveIntegerField(default=0, verbose_name='Брой спални')),
                ('description', models.TextField(verbose_name='Пълно описание')),
                ('description_bg', models.TextField(null=True, verbose_name='Пълно описание')),
                ('description_en', models.TextField(null=True, verbose_name='Пълно описание')),
                ('description_el', models.TextField(null=True, verbose_name='Пълно описание')),
                ('description_ru', models.TextField(null=True, verbose_name='Пълно описание')),
                ('is_published', models.BooleanField(default=True, verbose_name='Публикувана обява')),
                ('is_featured', models.BooleanField(default=False, verbose_name='Препоръчан имот')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на създаване')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Дата на обновяване')),
                ('assigned_broker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='properties', to='core.teammember', verbose_name='Отговорен брокер')),
                ('features', models.ManyToManyField(blank=True, to='properties.feature', verbose_name='Особености')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='properties.location', verbose_name='Локация')),
                ('property_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='properties.propertytype', verbose_name='Тип имот')),
            ],
            options={
                'verbose_name': 'Имот',
                'verbose_name_plural': 'Имоти',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PropertyImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=properties.models.get_property_image_upload_path, verbose_name='Снимка')),
                ('order', models.PositiveIntegerField(default=1, verbose_name='Поредност')),
                ('alt_text', models.CharField(blank=True, max_length=255, verbose_name='Алтернативен текст')),
                ('width', models.PositiveIntegerField(blank=True, null=True, verbose_name='Ширина')),
                ('height', models.PositiveIntegerField(blank=True, null=True, verbose_name='Височина')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='Размер на файла')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на създаване')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Дата на обновяване')),
                ('listing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='properties.property', verbose_name='Имот')),
            ],
            options={
                'verbose_name': 'Снимка на имот',
                'verbose_name_plural': 'Снимки на имоти',
                'ordering': ['order'],
                'unique_together': {('listing', 'order')},
            },
        ),
        migrations.CreateModel(
            name='PropertyVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('youtube_url', models.URLField(max_length=500, verbose_name='YouTube URL')),
                ('title', models.CharField(blank=True, max_length=200, verbose_name='Заглавие на видеото')),
                ('description', models.TextField(blank=True, verbose_name='Описание на видеото')),
                ('order', models.PositiveIntegerField(default=1, verbose_name='Поредност')),
                ('video_id', models.CharField(blank=True, max_length=20, verbose_name='YouTube Video ID')),
                ('thumbnail_url', models.URLField(blank=True, max_length=500, verbose_name='URL на thumbnail')),
                ('embed_url', models.URLField(blank=True, max_length=500, verbose_name='Embed URL')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на създаване')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Дата на обновяване')),
                ('listing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='properties.property', verbose_name='Имот')),
            ],
            options={
                'verbose_name': 'Видео на имот',
                'verbose_name_plural': 'Видеа на имоти',
                'ordering': ['order'],
                'unique_together': {('listing', 'order')},
            },
        ),
    ]
