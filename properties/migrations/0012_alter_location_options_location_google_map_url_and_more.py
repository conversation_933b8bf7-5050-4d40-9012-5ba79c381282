# Generated by Django 5.2.4 on 2025-07-27 16:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0011_alter_property_area_alter_property_is_featured'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='location',
            options={'ordering': ['city', 'name'], 'verbose_name': 'Адрес', 'verbose_name_plural': 'Адреси'},
        ),
        migrations.AddField(
            model_name='location',
            name='google_map_url',
            field=models.URLField(blank=True, null=True, verbose_name='URL на Google Map'),
        ),
        migrations.AlterField(
            model_name='property',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='properties.location', verbose_name='Адрес'),
        ),
    ]
