# Generated by Django 5.2.4 on 2025-07-31 09:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0017_alter_badge_options_badge_badge_bg_badge_badge_el_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='property',
            name='is_published',
        ),
        migrations.AddField(
            model_name='property',
            name='property_is_active',
            field=models.BooleanField(default=True, help_text='Тук можете да деактивирате обявата, без да я изтривате и по-късно да я активирате отново.', verbose_name='Активна обява'),
        ),
        migrations.AlterField(
            model_name='property',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Този имот ще бъде видим на главната страница, в секцията "Препоръчани имоти". Внимание: В Секцията "Препоръчани имоти" се показват максимално 6 имота', verbose_name='Топ оферта'),
        ),
    ]
