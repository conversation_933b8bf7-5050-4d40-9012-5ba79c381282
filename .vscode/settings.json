{
    "files.autoSave": "onFocusChange",
    "editor.formatOnSave": true,
    "editor.fontSize": 13,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "workbench.tree.indent": 16,
    // "workbench.colorCustomizations": {
    //     "tab.activeBorder": "#c0800b"
    // },
    "files.associations": {
        "*.css": "tailwindcss"
    },
    // "tailwindCSS.experimental.configFile": "theme/static_src/src/styles.css",
    "[django-html]": {
        "editor.defaultFormatter": "monosans.djlint"
        // "editor.tabSize": 4,
        // "editor.insertSpaces": true,
        // "editor.formatOnSave": true
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter"
    },
    "emmet.includeLanguages": {
        "django-html": "html"
    },
    "emmet.triggerExpansionOnTab": true,
    // "editor.tokenColorCustomizations": {
    //     "textMateRules": [
    //         {
    //             "scope": "string.unquoted.tag-string.django, storage.type.variable.django",
    //             "settings": {
    //                 "foreground": "#88c9db"
    //             }
    //         },
    //         {
    //             "scope": "entity.tag.tagbraces.django, storage.type.variable.django",
    //             "settings": {
    //                 "foreground": "#d0ca55"
    //             }
    //         },
    //         {
    //             "scope": "keyword.control.tag-name.django, storage.type.templatetag.django, keyword.operator.django",
    //             "settings": {
    //                 "foreground": "#d07bcd"
    //             }
    //         },
    //         {
    //             "scope": "variable.parameter.function-call.python",
    //             "settings": {
    //                 "foreground": "#7F7F7F"
    //             }
    //         }
    //     ]
    // },
    "cSpell.language": "en,bg_BG",
    "cSpell.words": [
        "adminsortable",
        "AUTOSLUG",
        "djlint",
        "dotenv",
        "endcomment",
        "endfor",
        "endwith",
        "forloop",
        "htmx",
        "imagekit",
        "Jazzmin",
        "linecap",
        "linejoin",
        "MODELTRANSLATION",
        "mouseenter",
        "mouseleave",
        "nunito",
        "preline",
        "siteconfig",
        "tinymce",
        "vite",
        "Берое",
        "Мерджанова",
        "подкатегория",
        "уебсайт",
        "уебсайта",
        "уебсайтове",
        "Уебсайтът",
        "хостинг",
        "Хостингът"
    ]
}
// {
//     "cSpell.words": [
//         "ecommerce",
//         "endcomment",
//         "htmx",
//         "linecap",
//         "linejoin",
//         "nowrap",
//     ]
// }