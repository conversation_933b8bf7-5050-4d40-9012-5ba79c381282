@import "tailwindcss";
@import "./buttons.css";

/**
  * A catch-all path to Django template files, JavaScript, and Python files
  * that contain Tailwind CSS classes and will be scanned by <PERSON><PERSON><PERSON> to generate the final CSS file.
  *
  * If your final CSS file is not being updated after code changes, you may want to broaden or narrow
  * the scope of this path.
  */
@source "../../../**/*.{html,py,js}";

@theme {
  /* Fonts */
  --font-title: "Merriweather", serif;
  --font-body: "Inter", sans-serif;

  /* Light Theme */
  --color-surface: var(--color-zinc-50);
  --color-surface-variant: var(--color-zinc-100);
  --color-on-surface: var(--color-neutral-600);
  --color-on-surface-variant: var(--color-neutral-500);
  --color-primary: var(--color-sky-700);
  --color-on-primary: var(--color-white);
  --color-secondary: var(--color-neutral-800);
  --color-on-secondary: var(--color-white);
  --color-outline: var(--color-zinc-300);

  /* Dark Theme */
  --color-surface-dark: var(--color-zinc-900);
  --color-surface-variant-dark: var(--color-zinc-800);
  --color-on-surface-dark: var(--color-zinc-200);
  --color-on-surface-variant-dark: var(--color-zinc-400);
  --color-primary-dark: var(--color-sky-600);
  --color-on-primary-dark: var(--color-white);
  --color-secondary-dark: var(--color-white);
  --color-on-secondary-dark: var(--color-black);
  --color-outline-dark: var(--color-zinc-700);

  /* Shared Colors */
  --color-accent: var(--color-pink-600);
  --color-on-accent: var(--color-neutral-100);
  --color-info: var(--color-sky-700);
  --color-on-info: var(--color-slate-100);
  --color-success: var(--color-green-700);
  --color-on-success: var(--color-white);
  --color-warning: var(--color-amber-600);
  --color-on-warning: var(--color-amber-50);
  --color-danger: var(--color-red-700);
  --color-on-danger: var(--color-slate-100);

  /* Border Radius */
  --radius-radius: var(--radius-lg);
}

/* Utility classes */
[data-theme="dark"] {
  --color-surface: var(--color-surface-dark);
  --color-surface-variant: var(--color-surface-variant-dark);
  --color-on-surface: var(--color-on-surface-dark);
  --color-on-surface-variant: var(--color-on-surface-variant-dark);
  --color-primary: var(--color-primary-dark);
  --color-on-primary: var(--color-on-primary-dark);
  --color-secondary: var(--color-secondary-dark);
  --color-on-secondary: var(--color-on-secondary-dark);
  --color-outline: var(--color-outline-dark);
}

@layer utilities {
  .scrollbar-hide {
    /* IE and Edge */
    -ms-overflow-style: none;

    /* Firefox */
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    /* Safari and Chrome */
    display: none;
  }
}
