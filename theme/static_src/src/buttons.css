@import "tailwindcss";

@layer components {
  .btn {
    @apply whitespace-nowrap rounded-radius px-5 py-2.5 text-base font-medium tracking-wide text-center transition hover:opacity-75 active:opacity-100 disabled:opacity-75 disabled:cursor-not-allowed focus-visible:outline-2 focus-visible:outline-offset-2 active:outline-offset-0;
  }

  /* Primary */
  .btn-primary {
    @apply bg-primary border border-primary text-on-primary focus-visible:outline-primary dark:bg-primary-dark dark:border-primary-dark dark:text-on-primary-dark dark:focus-visible:outline-primary-dark;
  }
  .btn-secondary {
    @apply bg-secondary border border-secondary text-on-secondary focus-visible:outline-secondary dark:bg-secondary-dark dark:border-secondary-dark dark:text-on-secondary-dark dark:focus-visible:outline-secondary-dark;
  }
  .btn-inverse {
    @apply bg-surface-dark border border-surface-dark text-on-surface-dark focus-visible:outline-surface-dark dark:bg-surface dark:border-surface dark:text-on-surface dark:focus-visible:outline-surface;
  }

  /* Outline Variant */
  .btn-outline {
    @apply bg-transparent;
  }

  .btn-primary.btn-outline {
    @apply text-primary dark:text-primary-dark;
  }
  .btn-secondary.btn-outline {
    @apply text-secondary dark:text-secondary-dark;
  }
  .btn-alternate.btn-outline {
    @apply text-outline dark:text-outline-dark;
  }
  .btn-inverse.btn-outline {
    @apply text-surface-dark dark:text-surface;
  }
  .btn-info.btn-outline {
    @apply text-info dark:text-info;
  }
  .btn-danger.btn-outline {
    @apply text-danger dark:text-danger;
  }
  .btn-warning.btn-outline {
    @apply text-warning dark:text-warning;
  }
  .btn-success.btn-outline {
    @apply text-success dark:text-success;
  }

  /* Ghost variant = like outline but no border */
  .btn-ghost {
    @apply bg-transparent border-0;
  }

  /* Sizes */
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  .btn-md {
    @apply px-4 py-2 text-sm;
  }
  .btn-default {
    @apply px-5 py-2.5 text-base;
  }
  .btn-lg {
    @apply px-6 py-3 text-lg;
  }
  .btn-xl {
    @apply px-7 py-4 text-xl;
  }
}
