document.addEventListener("alpine:init", () => {
  Alpine.data("themeManager", () => ({
    // Initialize theme from the data-theme attribute on the <html> element
    theme: document.documentElement.getAttribute("data-theme") || "light",

    init() {
      // The theme is already set by the inline script in <head>.
      // We just need to listen for changes from other tabs.
      window.addEventListener("storage", (event) => {
        if (event.key === "theme") {
          this.setTheme(event.newValue);
        }
      });
    },

    get darkMode() {
      return this.theme === "dark";
    },

    setTheme(newTheme) {
      this.theme = newTheme;
      localStorage.setItem("theme", newTheme);
      document.documentElement.setAttribute("data-theme", newTheme);
    },

    toggleTheme() {
      const newTheme = this.theme === "light" ? "dark" : "light";
      this.setTheme(newTheme);
    },
  }));

  // Компонент за мобилното меню
  Alpine.data("mobileMenu", () => ({
    open: false,

    toggle() {
      this.open = !this.open;
    },

    close() {
      this.open = false;
    },
  }));
});
