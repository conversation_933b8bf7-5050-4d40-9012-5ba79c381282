"""
Тестове за функционалността на localStorage в компонента за смяна на езика
"""

from django.test import TestCase, Client
from django.urls import reverse


class LanguageSwitcherLocalStorageTests(TestCase):
    """Тестове за localStorage функционалността на смяната на езика"""

    def setUp(self):
        """Настройка на тестови данни"""
        self.client = Client()

    def test_language_switcher_component_has_localStorage_functionality(self):
        """Тества дали компонентът за смяна на езика има localStorage функционалност"""

        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали Alpine.js компонентът е в темплейта
        self.assertContains(response, 'x-data="languageSwitcher"')

        # Проверява дали има методи за localStorage (чрез Vite)
        response_content = response.content.decode("utf-8")

        # Проверява дали JavaScript файлът се зарежда чрез Vite
        self.assertContains(response, "main.js")

    def test_language_switcher_has_loading_indicators(self):
        """Тества дали компонентът има индикатори за зареждане"""

        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали има loading индикатори в темплейта
        self.assertContains(response, "isLoading")
        self.assertContains(response, "animate-spin")

    def test_language_switcher_has_current_language_indicators(self):
        """Тества дали компонентът показва текущо избрания език"""

        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали има индикатори за текущия език
        self.assertContains(response, "isCurrentLanguage")

        # Проверява дали има checkmark за текущия език
        self.assertContains(response, "M5 13l4 4L19 7")  # SVG path за checkmark

    def test_all_supported_languages_work(self):
        """Тества дали всички поддържани езици работят"""

        supported_languages = ["bg", "en", "el", "ru"]

        for lang_code in supported_languages:
            response = self.client.get(f"/{lang_code}/")
            self.assertEqual(response.status_code, 200)

            # Проверява дали компонентът се зарежда за всеки език
            self.assertContains(response, 'x-data="languageSwitcher"')

    def test_language_switcher_preserves_url_parameters(self):
        """Тества дали смяната на езика запазва URL параметрите"""

        # Тестваме с URL параметри
        response = self.client.get("/bg/?search=test&page=2")
        self.assertEqual(response.status_code, 200)

        # Проверяваме дали компонентът се зарежда правилно
        self.assertContains(response, 'x-data="languageSwitcher"')

    def test_language_switcher_mobile_version_exists(self):
        """Тества дали мобилната версия на компонента съществува"""

        response = self.client.get("/bg/")
        self.assertEqual(response.status_code, 200)

        # Проверява дали има мобилна секция
        self.assertContains(response, "Language Switcher Section (Mobile)")

        # Проверява дали мобилната версия има същата функционалност
        self.assertContains(response, "grid grid-cols-2 gap-2")

    def tearDown(self):
        """Почистване след тестове"""
        pass
