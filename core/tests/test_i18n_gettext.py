"""
Тестове за използването на gettext_lazy в Python файловете
"""

from django.test import TestCase
from django.utils.translation import gettext_lazy as _
from django.utils.translation import override as translation_override
from django.utils.functional import Promise
from core.models import City, Property, TeamMember, Location, PropertyType, Feature


class GettextLazyTests(TestCase):
    """Тестове за правилното използване на gettext_lazy в моделите"""

    def test_model_verbose_names_are_translatable(self):
        """Тества дали verbose_name полетата в моделите са преводими"""

        # Тест за City модел
        city_meta = City._meta
        # gettext_lazy връща Promise обекти, които се превръщат в стрингове при нужда
        self.assertTrue(isinstance(city_meta.verbose_name, (str, Promise)))
        self.assertTrue(isinstance(city_meta.verbose_name_plural, (str, Promise)))

        # Проверява дали полетата са маркирани за превод
        name_field = city_meta.get_field("name")
        self.assertTrue(hasattr(name_field, "verbose_name"))
        self.assertTrue(isinstance(name_field.verbose_name, (str, Promise)))

    def test_admin_fieldset_names_are_translatable(self):
        """Тества дали fieldset имената в админ панела са преводими"""
        from core.admin import PropertyAdmin, ContactInquiryAdmin

        # Проверява PropertyAdmin fieldsets
        property_admin = PropertyAdmin(Property, None)
        fieldsets = property_admin.fieldsets

        self.assertTrue(len(fieldsets) > 0)
        # Проверява дали първият fieldset има име
        first_fieldset_name = fieldsets[0][0]
        self.assertTrue(isinstance(first_fieldset_name, (str, Promise)))

    def test_translation_context_switching(self):
        """Тества дали преводите работят при смяна на езика"""

        # Създаваме тестов град
        city = City.objects.create(name="Тестов град")

        # Проверяваме дали моделът се създава успешно
        self.assertEqual(city.name, "Тестов град")

        # Проверяваме дали meta информацията е достъпна
        self.assertTrue(hasattr(City._meta, "verbose_name"))
        self.assertTrue(hasattr(City._meta, "verbose_name_plural"))

    def test_property_model_translation_fields(self):
        """Тества специално Property модела заради неговата сложност"""
        # Създаваме необходимите обекти
        city = City.objects.create(name="София")
        location = Location.objects.create(name="Център", city=city)
        property_type = PropertyType.objects.create(name="Апартамент")
        broker = TeamMember.objects.create(
            name="Тест Брокер",
            email="<EMAIL>",
            phone="123456789",
            title="Консултант",
        )

        # Създаваме имот
        property_obj = Property.objects.create(
            title="Тестов Имот",
            slug="testov-imot",
            location=location,
            property_type=property_type,
            price=200000,
            area=100,
            description="Описание на тестовия имот",
            assigned_broker=broker,
            is_published=True,
        )

        # Проверяваме дали всички полета имат verbose_name
        meta = Property._meta
        field_names = ["title", "price", "area", "rooms", "description"]

        for field_name in field_names:
            field = meta.get_field(field_name)
            self.assertTrue(hasattr(field, "verbose_name"))
            self.assertTrue(isinstance(field.verbose_name, (str, Promise)))

    def test_feature_and_property_type_translation(self):
        """Тества дали Feature и PropertyType моделите са правилно конфигурирани за превод"""

        # Създаваме Feature
        feature = Feature.objects.create(name="Гараж")
        feature_meta = Feature._meta

        self.assertTrue(hasattr(feature_meta, "verbose_name"))
        self.assertTrue(hasattr(feature_meta, "verbose_name_plural"))

        # Създаваме PropertyType
        prop_type = PropertyType.objects.create(name="Апартамент")
        prop_type_meta = PropertyType._meta

        self.assertTrue(hasattr(prop_type_meta, "verbose_name"))
        self.assertTrue(hasattr(prop_type_meta, "verbose_name_plural"))

    def test_import_gettext_lazy_in_models(self):
        """Тества дали gettext_lazy е правилно импортиран в models.py"""
        import core.models as models_module

        # Проверява дали модулът има _ функцията (gettext_lazy)
        self.assertTrue(hasattr(models_module, "_"))

    def test_import_gettext_lazy_in_admin(self):
        """Тества дали gettext_lazy е правилно импортиран в admin.py"""
        import core.admin as admin_module

        # Проверява дали модулът има _ функцията (gettext_lazy)
        self.assertTrue(hasattr(admin_module, "_"))
