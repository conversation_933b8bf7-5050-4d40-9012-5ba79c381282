"""
Тестове за обработка на изображения
"""

import os
import tempfile
from io import BytesIO
from PIL import Image
from django.test import TestCase, override_settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.exceptions import ValidationError
from django.conf import settings
from core.models import (
    Property,
    PropertyImage,
    PropertyType,
    Location,
    TeamMember,
    City,
)
from core.utils import (
    optimize_image,
    validate_image_file,
    get_image_info,
    fix_image_orientation,
    create_image_variants,
)
from core.forms import PropertyImageForm


class ImageProcessingTestCase(TestCase):
    """Тестове за утилитите за обработка на изображения"""

    def setUp(self):
        """Настройка на тестовите данни"""
        # Създаваме необходимите обекти за тестовете
        self.city = City.objects.create(name="Тестов град")
        self.location = Location.objects.create(name="Тестова локация", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.broker = TeamMember.objects.create(
            name="Тестов брокер",
            title="Брокер",
            phone="0888123456",
            email="<EMAIL>",
        )
        self.property = Property.objects.create(
            title="Тестов имот",
            property_type=self.property_type,
            location=self.location,
            price=100000,
            area=100,
            description="Тестово описание",
            assigned_broker=self.broker,
        )

    def create_test_image(self, width=800, height=600, format="JPEG"):
        """Създава тестово изображение"""
        image = Image.new("RGB", (width, height), color="red")
        image_io = BytesIO()
        image.save(image_io, format=format)
        image_io.seek(0)
        return image_io

    def create_uploaded_file(
        self, width=800, height=600, format="JPEG", filename="test.jpg"
    ):
        """Създава Django UploadedFile за тестване"""
        image_io = self.create_test_image(width, height, format)
        return SimpleUploadedFile(
            filename, image_io.getvalue(), content_type=f"image/{format.lower()}"
        )

    def test_optimize_image_basic(self):
        """Тества основната функционалност на optimize_image"""
        test_image = self.create_test_image(1200, 900)
        optimized = optimize_image(test_image, max_width=800, max_height=600)

        self.assertIsNotNone(optimized)

        # Проверяваме дали изображението е преоразмерено
        optimized_img = Image.open(optimized)
        self.assertLessEqual(optimized_img.width, 800)
        self.assertLessEqual(optimized_img.height, 600)

    def test_validate_image_file_valid(self):
        """Тества валидация на валиден файл с изображение"""
        valid_image = self.create_uploaded_file()
        is_valid, error_message = validate_image_file(valid_image)

        self.assertTrue(is_valid)
        self.assertEqual(error_message, "")

    def test_validate_image_file_too_large(self):
        """Тества валидация на твърде голям файл"""
        # Създаваме голямо изображение
        large_image = self.create_uploaded_file(width=6000, height=4000)
        is_valid, error_message = validate_image_file(large_image)

        self.assertFalse(is_valid)
        self.assertIn("твърде голямо", error_message)

    def test_validate_image_file_too_small(self):
        """Тества валидация на твърде малко изображение"""
        small_image = self.create_uploaded_file(width=200, height=150)
        is_valid, error_message = validate_image_file(small_image)

        self.assertFalse(is_valid)
        self.assertIn("твърде малко", error_message)

    def test_get_image_info(self):
        """Тества извличането на информация за изображението"""
        test_image = self.create_uploaded_file(width=800, height=600)
        info = get_image_info(test_image)

        self.assertEqual(info["width"], 800)
        self.assertEqual(info["height"], 600)
        self.assertEqual(info["format"], "JPEG")
        self.assertGreater(info["size_bytes"], 0)

    def test_fix_image_orientation(self):
        """Тества корекцията на ориентацията на изображението"""
        # Създаваме тестово изображение
        image = Image.new("RGB", (800, 600), color="blue")

        # Тестваме функцията (без EXIF данни тя трябва да върне същото изображение)
        fixed_image = fix_image_orientation(image)

        self.assertEqual(fixed_image.size, (800, 600))


class PropertyImageModelTestCase(TestCase):
    """Тестове за PropertyImage модела"""

    def setUp(self):
        """Настройка на тестовите данни"""
        self.city = City.objects.create(name="Тестов град")
        self.location = Location.objects.create(name="Тестова локация", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.broker = TeamMember.objects.create(
            name="Тестов брокер",
            title="Брокер",
            phone="0888123456",
            email="<EMAIL>",
        )
        self.property = Property.objects.create(
            title="Тестов имот",
            property_type=self.property_type,
            location=self.location,
            price=100000,
            area=100,
            description="Тестово описание",
            assigned_broker=self.broker,
        )

    def create_uploaded_file(self, width=800, height=600, filename="test.jpg"):
        """Създава тестов файл за качване"""
        image = Image.new("RGB", (width, height), color="green")
        image_io = BytesIO()
        image.save(image_io, format="JPEG")
        image_io.seek(0)
        return SimpleUploadedFile(
            filename, image_io.getvalue(), content_type="image/jpeg"
        )

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def test_property_image_creation(self):
        """Тества създаването на PropertyImage"""
        test_file = self.create_uploaded_file()

        property_image = PropertyImage.objects.create(
            property_obj=self.property, image=test_file, order=1
        )

        self.assertEqual(property_image.property_obj, self.property)
        self.assertEqual(property_image.order, 1)
        self.assertTrue(property_image.image)
        self.assertIsNotNone(property_image.width)
        self.assertIsNotNone(property_image.height)
        self.assertIsNotNone(property_image.file_size)

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def test_property_image_auto_alt_text(self):
        """Тества автоматичното генериране на alt текст"""
        test_file = self.create_uploaded_file()

        property_image = PropertyImage.objects.create(
            property_obj=self.property, image=test_file, order=1
        )

        expected_alt = f"Снимка 1 от {self.property.title}"
        self.assertEqual(property_image.alt_text, expected_alt)

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def test_property_image_aspect_ratio(self):
        """Тества изчисляването на съотношението ширина/височина"""
        test_file = self.create_uploaded_file(width=800, height=600)

        property_image = PropertyImage.objects.create(
            property_obj=self.property, image=test_file, order=1
        )

        expected_ratio = round(800 / 600, 2)
        self.assertEqual(property_image.aspect_ratio, expected_ratio)

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def test_property_image_file_size_formatted(self):
        """Тества форматирането на размера на файла"""
        test_file = self.create_uploaded_file()

        property_image = PropertyImage.objects.create(
            property_obj=self.property, image=test_file, order=1
        )

        formatted_size = property_image.file_size_formatted
        self.assertIn("KB", formatted_size)  # Очакваме размера да е в KB

    def test_property_main_image(self):
        """Тества property.main_image свойството"""
        # Създаваме няколко снимки
        test_file1 = self.create_uploaded_file(filename="test1.jpg")
        test_file2 = self.create_uploaded_file(filename="test2.jpg")

        with override_settings(MEDIA_ROOT=tempfile.mkdtemp()):
            PropertyImage.objects.create(
                property_obj=self.property, image=test_file2, order=2
            )
            main_image = PropertyImage.objects.create(
                property_obj=self.property, image=test_file1, order=1
            )

            # Проверяваме дали main_image връща правилната снимка
            self.assertEqual(self.property.main_image, main_image)


class PropertyImageFormTestCase(TestCase):
    """Тестове за PropertyImageForm"""

    def setUp(self):
        """Настройка на тестовите данни"""
        self.city = City.objects.create(name="Тестов град")
        self.location = Location.objects.create(name="Тестова локация", city=self.city)
        self.property_type = PropertyType.objects.create(name="Апартамент")
        self.broker = TeamMember.objects.create(
            name="Тестов брокер",
            title="Брокер",
            phone="0888123456",
            email="<EMAIL>",
        )
        self.property = Property.objects.create(
            title="Тестов имот",
            property_type=self.property_type,
            location=self.location,
            price=100000,
            area=100,
            description="Тестово описание",
            assigned_broker=self.broker,
        )

    def create_uploaded_file(self, width=800, height=600, filename="test.jpg"):
        """Създава тестов файл за качване"""
        image = Image.new("RGB", (width, height), color="blue")
        image_io = BytesIO()
        image.save(image_io, format="JPEG")
        image_io.seek(0)
        return SimpleUploadedFile(
            filename, image_io.getvalue(), content_type="image/jpeg"
        )

    def test_valid_form(self):
        """Тества валидна форма"""
        test_file = self.create_uploaded_file()

        form_data = {"order": 1, "alt_text": "Тестов alt текст"}
        form = PropertyImageForm(data=form_data, files={"image": test_file})

        self.assertTrue(form.is_valid())

    def test_invalid_image_size(self):
        """Тества форма с невалиден размер на изображението"""
        # Създаваме твърде малко изображение
        small_image = self.create_uploaded_file(width=200, height=150)

        form_data = {"order": 1}
        form = PropertyImageForm(data=form_data, files={"image": small_image})

        self.assertFalse(form.is_valid())
        self.assertIn("image", form.errors)

    @override_settings(MEDIA_ROOT=tempfile.mkdtemp())
    def test_existing_image_validation(self):
        """Тества валидация на съществуващо изображение (ImageFieldFile)"""
        # Създаваме PropertyImage с изображение
        test_file = self.create_uploaded_file()

        property_image = PropertyImage.objects.create(
            property_obj=self.property, image=test_file, order=1
        )

        # Тестваме валидацията на съществуващото изображение
        from core.utils import validate_image_file

        is_valid, error_message = validate_image_file(property_image.image)

        # Трябва да е валидно дори че е ImageFieldFile, не UploadedFile
        self.assertTrue(is_valid)
        self.assertEqual(error_message, "")
