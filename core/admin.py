from django import forms
from django.contrib import admin
from modeltranslation.admin import TabbedTranslationAdmin, TranslationStackedInline
from adminsortable2.admin import SortableAdminBase, SortableInlineAdminMixin
from tinymce.widgets import TinyMCE

from .models import (
    ContactInquiry,
    CTASection,
    FeaturedPropertiesSection,
    HeroSection,
    TeamMember,
    WhyUsSection,
    WhyUsSectionCard,
    SiteAboutUs,
    ValueCard,
    ContactPage,
    PrivacyPolicy,
    TermsOfService,
)

admin.site.site_header = "Имоти Мерджанова Администрация"
admin.site.site_title = "Административен панел | Имоти Мерджанова"
admin.site.index_title = ""


@admin.register(TeamMember)
class TeamMemberAdmin(TabbedTranslationAdmin):
    list_display = ("name", "title", "phone", "email")
    list_filter = ("title",)
    search_fields = ("name", "title", "email")


@admin.register(ContactInquiry)
class ContactInquiryAdmin(admin.ModelAdmin):
    list_display = ("name", "email", "listing", "created_at", "is_handled")
    list_filter = ("is_handled", "created_at", "listing__property_type")
    search_fields = ("name", "email", "message", "listing__title")
    readonly_fields = ("name", "email", "phone", "message", "listing", "created_at")
    list_editable = ("is_handled",)
    date_hierarchy = "created_at"

    fieldsets = (
        (
            "Информация за контакта",
            {"fields": ("name", "email", "phone", "created_at")},
        ),
        ("Запитване", {"fields": ("listing", "message")}),
        ("Статус", {"fields": ("is_handled",)}),
    )

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(HeroSection)
class HeroSectionAdmin(TabbedTranslationAdmin):
    list_display = ("is_active", "heading", "subheading")
    list_display_links = ("heading", "subheading")

    class Media:
        css = {"all": ("admin/css/custom_admin.css",)}


@admin.register(FeaturedPropertiesSection)
class FeaturedPropertiesSectionAdmin(TabbedTranslationAdmin):
    list_display = ("is_active", "heading", "subheading")
    list_display_links = ("heading", "subheading")

    class Media:
        css = {"all": ("admin/css/custom_admin.css",)}


class WhyUsSectionCardInlineForm(forms.ModelForm):
    class Meta:
        model = WhyUsSectionCard
        fields = "__all__"
        widgets = {
            "description": forms.Textarea(attrs={"rows": 3}),
        }


class WhyUsSectionCardInline(SortableInlineAdminMixin, TranslationStackedInline):
    model = WhyUsSectionCard
    form = WhyUsSectionCardInlineForm
    extra = 0
    fields = [
        "is_active",
        "title",
        "description",
        "icon",
        "order",
    ]
    ordering = ["order"]
    classes = ["collapse"]


@admin.register(WhyUsSection)
class WhyUsSectionAdmin(SortableAdminBase, TabbedTranslationAdmin):
    inlines = [WhyUsSectionCardInline]
    list_display = ["is_active", "heading", "subheading"]
    list_display_links = ["heading", "subheading"]

    class Media:
        css = {"all": ("admin/css/custom_admin.css",)}


@admin.register(CTASection)
class CTASectionAdmin(TabbedTranslationAdmin):
    list_display = ("is_active", "heading", "subheading")
    list_display_links = ("heading", "subheading")

    class Media:
        css = {"all": ("admin/css/custom_admin.css",)}


class ValueCardInline(TranslationStackedInline):
    model = ValueCard
    extra = 1
    fields = ("order", "icon", "title", "description")
    classes = ["collapse"]


@admin.register(SiteAboutUs)
class SiteAboutUsAdmin(TabbedTranslationAdmin):
    inlines = [ValueCardInline]
    fieldsets = (
        (
            "Основни параметри",
            {
                "fields": (
                    "heading",
                    "subheading",
                )
            },
        ),
        (
            "Секция история",
            {
                "fields": (
                    "our_history_heading",
                    "our_history_content",
                )
            },
        ),
        (
            "Секция ценности",
            {
                "fields": (
                    "our_values_heading",
                    "our_values_subheading",
                )
            },
        ),
        (
            "Секция на нашия екип",
            {
                "fields": (
                    "our_team_heading",
                    "our_team_subheading",
                )
            },
        ),
        (
            "Секция призив към действие",
            {
                "fields": (
                    "cta_heading",
                    "cta_subheading",
                )
            },
        ),
    )

    def has_add_permission(self, request):
        return not SiteAboutUs.objects.exists()

    def has_delete_permission(self, request, obj=None):
        return False

    def changelist_view(self, request, extra_context=None):
        obj = SiteAboutUs.objects.first()
        if obj:
            from django.urls import reverse
            from django.shortcuts import redirect

            return redirect(reverse("admin:core_siteaboutus_change", args=[obj.id]))
        return super().changelist_view(request, extra_context)


@admin.register(ContactPage)
class ContactPageAdmin(TabbedTranslationAdmin):

    fieldsets = (
        (
            "Основни параметри",
            {
                "fields": (
                    "heading",
                    "subheading",
                )
            },
        ),
        (
            "Контакт",
            {"fields": ("contact_heading",)},
        ),
        (
            "Съобщение",
            {
                "fields": (
                    "send_message_heading",
                    "send_message_subheading",
                )
            },
        ),
        (
            "Карта",
            {
                "fields": (
                    "map_heading",
                    "map_subheading",
                )
            },
        ),
    )

    def has_add_permission(self, request):
        return not ContactPage.objects.exists()

    def has_delete_permission(self, request, obj=None):
        return False

    def changelist_view(self, request, extra_context=None):
        obj = ContactPage.objects.first()
        if obj:
            from django.urls import reverse
            from django.shortcuts import redirect

            return redirect(reverse("admin:core_contactpage_change", args=[obj.id]))
        return super().changelist_view(request, extra_context)


class PrivacyPolicyAdminForm(forms.ModelForm):
    """Custom form for PrivacyPolicy with TinyMCE widgets for all translated text fields"""

    class Meta:
        model = PrivacyPolicy
        fields = "__all__"
        widgets = {
            "text": TinyMCE(attrs={"cols": 80, "rows": 30}),
            "text_bg": TinyMCE(attrs={"cols": 80, "rows": 30}),
            "text_en": TinyMCE(attrs={"cols": 80, "rows": 30}),
        }


@admin.register(PrivacyPolicy)
class PrivacyPolicyAdmin(TabbedTranslationAdmin):
    form = PrivacyPolicyAdminForm

    def has_add_permission(self, request):
        return not PrivacyPolicy.objects.exists()

    def has_delete_permission(self, request, obj=None):
        return False

    def changelist_view(self, request, extra_context=None):
        obj = PrivacyPolicy.objects.first()
        if obj:
            from django.urls import reverse
            from django.shortcuts import redirect

            return redirect(reverse("admin:core_privacypolicy_change", args=[obj.id]))
        return super().changelist_view(request, extra_context)


class TermsOfServiceAdminForm(forms.ModelForm):
    """Custom form for TermsOfService with TinyMCE widgets for all translated text fields"""

    class Meta:
        model = TermsOfService
        fields = "__all__"
        widgets = {
            "text": TinyMCE(attrs={"cols": 80, "rows": 30}),
            "text_bg": TinyMCE(attrs={"cols": 80, "rows": 30}),
            "text_en": TinyMCE(attrs={"cols": 80, "rows": 30}),
        }


@admin.register(TermsOfService)
class TermsOfServiceAdmin(TabbedTranslationAdmin):
    form = TermsOfServiceAdminForm

    def has_add_permission(self, request):
        return not TermsOfService.objects.exists()

    def has_delete_permission(self, request, obj=None):
        return False

    def changelist_view(self, request, extra_context=None):
        obj = TermsOfService.objects.first()
        if obj:
            from django.urls import reverse
            from django.shortcuts import redirect

            return redirect(reverse("admin:core_termsofservice_change", args=[obj.id]))
        return super().changelist_view(request, extra_context)
