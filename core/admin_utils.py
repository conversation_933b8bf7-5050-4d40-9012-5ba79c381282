from django.utils.html import format_html, mark_safe


def image_preview(obj):
    """Генерира HTML миниатюра за изображение"""
    if obj.image:
        return format_html(
            '<img src="{}" style="max-width: 100px; max-height: 100px; object-fit: cover;" />',
            obj.thumbnail.url if hasattr(obj, "thumbnail") else obj.image.url,
        )
    return "Няма изображение"


image_preview.short_description = "Преглед"


def render_admin_message(
    message: str, color: str = "#cce5ff", border_color: str = "#b8daff"
):
    return mark_safe(
        f"<div style='max-width:400px; padding:10px; background-color: {color}; "
        f"border-left: 5px solid {border_color}; margin-bottom: 10px;'>"
        f"{message}</div>"
    )


def warning_message(message: str):
    return render_admin_message(message, color="#fff3cd", border_color="#ffeeba")


def info_message(message: str):
    return render_admin_message(message, color="#d1ecf1", border_color="#bee5eb")


def success_message(message: str):
    return render_admin_message(message, color="#d4edda", border_color="#c3e6cb")


def error_message(message: str):
    return render_admin_message(message, color="#f8d7da", border_color="#f5c6cb")
