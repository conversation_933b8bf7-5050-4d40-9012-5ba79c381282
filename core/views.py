from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.views.generic import TemplateView

from properties.models import City, Property, PropertyType

from .models import (
    ContactInquiry,
    CTASection,
    FeaturedPropertiesSection,
    HeroSection,
    TeamMember,
    WhyUsSection,
    WhyUsSectionCard,
    SiteAboutUs,
    ContactPage,
    PrivacyPolicy,
    TermsOfService,
)


def home_view(request):
    """Начална страница с препоръчани имоти и отзиви"""
    featured_properties = Property.objects.filter(
        property_is_active=True, is_featured=True
    ).select_related("location", "property_type")[:6]

    property_types = PropertyType.objects.all().order_by("name")
    locations = City.objects.all().order_by("name")

    hero_section = HeroSection.objects.filter(is_active=True).first()
    featured_properties_section = FeaturedPropertiesSection.objects.filter(
        is_active=True
    ).first()
    why_us_section = WhyUsSection.objects.filter(is_active=True).first()
    why_us_section_cards = WhyUsSectionCard.objects.filter(is_active=True)[:3]

    cta_section = CTASection.objects.filter(is_active=True).first()

    context = {
        "featured_properties": featured_properties,
        "property_types": property_types,
        "locations": locations,
        "hero_section": hero_section,
        "featured_properties_section": featured_properties_section,
        "why_us_section": why_us_section,
        "why_us_section_cards": why_us_section_cards,
        "cta_section": cta_section,
    }
    return render(request, "core/index.html", context)


def property_list_view(request):
    """Страница с всички имоти и филтриране"""
    properties = (
        Property.objects.filter(property_is_active=True)
        .select_related("location", "property_type")
        .prefetch_related("features", "images")
    )

    property_type = request.GET.get("property_type")
    location_id = request.GET.get("location")  # Вече ще е ID-то на града
    min_price = request.GET.get("min_price")
    max_price = request.GET.get("max_price")
    min_area = request.GET.get("min_area")
    max_area = request.GET.get("max_area")
    rooms = request.GET.get("rooms")
    features = request.GET.getlist("features")  # Може да има няколко
    search_query = request.GET.get("search")
    sort_by = request.GET.get(
        "sort_by", "-created_at"
    )  # По подразбиране най-нови първо

    if property_type:
        properties = properties.filter(property_type__slug=property_type)

    if location_id:
        properties = properties.filter(location__city__id=location_id)

    if min_price:
        properties = properties.filter(price__gte=min_price)

    if max_price:
        properties = properties.filter(price__lte=max_price)

    if min_area:
        properties = properties.filter(area__gte=min_area)

    if max_area:
        properties = properties.filter(area__lte=max_area)

    if rooms:
        properties = properties.filter(rooms=rooms)

    if features:
        for feature_id in features:
            properties = properties.filter(features__id=feature_id)

    if search_query:
        properties = properties.filter(
            Q(title__icontains=search_query)
            | Q(description__icontains=search_query)
            | Q(location__name__icontains=search_query)
            | Q(location__city__name__icontains=search_query)
        )

    if sort_by in ["price", "-price", "area", "-area", "created_at", "-created_at"]:
        properties = properties.order_by(sort_by)

    property_types = PropertyType.objects.all()
    locations = City.objects.all()  # Градове за филтъра
    all_features = Feature.objects.all()

    context = {
        "properties": properties,
        "property_types": property_types,
        "locations": locations,
        "all_features": all_features,
        "total_count": properties.count(),
        "current_filters": {
            "property_type": property_type,
            "location": location_id,
            "min_price": min_price,
            "max_price": max_price,
            "min_area": min_area,
            "max_area": max_area,
            "rooms": rooms,
            "features": features,
            "search": search_query,
            "sort_by": sort_by,
        },
    }

    if request.headers.get("HX-Request"):
        response = render(request, "partials/property_list_results.html", context)
        response["X-Total-Count"] = str(context["total_count"])
        return response

    return render(request, "core/property_list.html", context)


def property_detail_view(request, slug):
    """Детайлна страница за конкретен имот"""
    property = get_object_or_404(
        Property.objects.select_related(
            "location", "property_type", "assigned_broker"
        ).prefetch_related("features", "images"),
        slug=slug,
        property_is_active=True,
    )

    similar_properties = (
        Property.objects.filter(
            property_type=property.property_type,
            location=property.location,
            property_is_active=True,
        )
        .exclude(id=property.id)
        .select_related("location", "property_type")[:4]
    )

    context = {
        "property": property,
        "similar_properties": similar_properties,
    }
    return render(request, "core/property_detail.html", context)


def contact_inquiry_create_view(request):
    """Обработва POST заявки от формите за контакт"""
    if request.method == "POST":
        name = request.POST.get("name")
        email = request.POST.get("email")
        phone = request.POST.get("phone")
        message = request.POST.get("message")
        property_id = request.POST.get("property_id")

        if not all([name, email, message]):
            return JsonResponse(
                {
                    "success": False,
                    "error": "Моля попълнете всички задължителни полета.",
                }
            )

        inquiry_data = {
            "name": name,
            "email": email,
            "phone": phone or "",
            "message": message,
        }

        if property_id:
            try:
                property = Property.objects.get(id=property_id, property_is_active=True)
                inquiry_data["property"] = property
            except Property.DoesNotExist:
                pass

        ContactInquiry.objects.create(**inquiry_data)

        return JsonResponse(
            {
                "success": True,
                "message": "Благодарим за запитването! Ще се свържем с Вас скоро.",
            }
        )

    return JsonResponse({"success": False, "error": "Невалиден метод"})


class AboutView(TemplateView):
    """За нас страница"""

    template_name = "core/about.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["site_about_us"] = SiteAboutUs.objects.first()
        context["team_members"] = TeamMember.objects.all()
        return context


class ContactView(TemplateView):
    """Контакти страница"""

    template_name = "core/contact.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["contact_page"] = ContactPage.objects.first()
        return context


class PrivacyPolicyView(TemplateView):
    """Политика за поверителност"""

    template_name = "core/privacy_policy.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["privacy_policy"] = PrivacyPolicy.objects.first()
        return context


class TermsOfServiceView(TemplateView):
    """Общи условия"""

    template_name = "core/terms_of_service.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["terms_of_service"] = TermsOfService.objects.first()
        return context
