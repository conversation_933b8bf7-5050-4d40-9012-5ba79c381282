from django.db import models
from django.urls import reverse
from django.core.files.base import ContentFile
from autoslug import AutoSlugField
from imagekit.models import ImageSpecField
from imagekit.processors import ResizeToFit, Thumbnail


class TeamMember(models.Model):
    """Модел за членовете на екипа (брокери)"""

    name = models.CharField(max_length=100, verbose_name="Име")
    photo = models.ImageField(
        upload_to="team_photos/", verbose_name="Снимка", blank=True, null=True
    )
    phone = models.CharField(max_length=20, verbose_name="Телефонен номер")
    email = models.EmailField(verbose_name="Имейл адрес")
    title = models.CharField(max_length=100, verbose_name="Длъжност")

    class Meta:
        verbose_name = "Член на екипа"
        verbose_name_plural = "Членове на екипа"
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.title}"


class ContactInquiry(models.Model):
    """Модел за запитвания от контактната форма"""

    name = models.CharField(max_length=100, verbose_name="Име")
    email = models.EmailField(verbose_name="Имейл адрес")
    phone = models.CharField(max_length=20, verbose_name="Телефонен номер")
    message = models.TextField(verbose_name="Съобщение")
    listing = models.ForeignKey(
        "properties.Property",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Имот (ако е за конкретна обява)",
    )
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата на получаване"
    )
    is_handled = models.BooleanField(default=False, verbose_name="Обработено запитване")

    class Meta:
        verbose_name = "Запитване за контакт"
        verbose_name_plural = "Запитвания за контакт"
        ordering = ["-created_at"]

    def __str__(self):
        if self.listing:
            return f"Запитване от {self.name} за {self.listing.title}"
        return f"Общо запитване от {self.name}"


# Sections


class HeroSection(models.Model):
    is_active = models.BooleanField(default=False, verbose_name="Активна")
    heading = models.CharField(max_length=100, verbose_name="Заглавие")
    subheading = models.CharField(max_length=100, verbose_name="Подзаглавие")
    image = models.ImageField(
        upload_to="hero_section/",
        verbose_name="Снимка",
        help_text="Внимание: Снимката автоматично ще бъде затъмнена.",
    )

    class Meta:
        verbose_name = "1. Секция"
        verbose_name_plural = "1. Секция"
        ordering = ["-is_active"]

    def __str__(self):
        return self.heading


class FeaturedPropertiesSection(models.Model):
    heading = models.CharField(max_length=100, verbose_name="Заглавие")
    subheading = models.CharField(max_length=100, verbose_name="Подзаглавие")
    is_active = models.BooleanField(default=False, verbose_name="Активна")

    class Meta:
        verbose_name = "2. Препоръчани Имоти"
        verbose_name_plural = "2. Препоръчани Имоти"
        ordering = ["-is_active"]

    def __str__(self):
        return self.heading


class WhyUsSection(models.Model):
    is_active = models.BooleanField(default=False, verbose_name="Активна")
    heading = models.CharField(max_length=100, verbose_name="Заглавие")
    subheading = models.CharField(max_length=100, verbose_name="Подзаглавие")

    class Meta:
        verbose_name = "3. Защо да изберете нас?"
        verbose_name_plural = "3. Защо да изберете нас?"
        ordering = ["-is_active"]

    def __str__(self):
        return self.heading


class WhyUsSectionCard(models.Model):
    section = models.ForeignKey(
        WhyUsSection,
        on_delete=models.CASCADE,
        related_name="cards",
        verbose_name="Секция",
    )
    is_active = models.BooleanField(default=True, verbose_name="Активна")
    title = models.CharField(max_length=100, verbose_name="Заглавие")
    description = models.TextField(verbose_name="Описание")
    icon = models.CharField(max_length=20, verbose_name="Иконка")
    order = models.PositiveIntegerField(default=0, verbose_name="Поредност")

    class Meta:
        verbose_name = "Картичка"
        verbose_name_plural = "Картички"
        ordering = ["order"]

    def __str__(self):
        return self.title


class CTASection(models.Model):
    is_active = models.BooleanField(default=False, verbose_name="Активна")
    heading = models.CharField(max_length=100, verbose_name="Заглавие")
    subheading = models.CharField(max_length=100, verbose_name="Подзаглавие")

    class Meta:
        verbose_name = "4. CTA"
        verbose_name_plural = "4. CTA"
        ordering = ["-is_active"]

    def __str__(self):
        return self.heading


class SiteAboutUs(models.Model):
    heading = models.CharField(max_length=255, verbose_name="Заглавие")
    subheading = models.CharField(max_length=255, verbose_name="Подзаглавие")

    our_history_heading = models.CharField(
        max_length=255, verbose_name="Заглавие на историята"
    )
    our_history_content = models.TextField(verbose_name="Съдържание на историята")

    our_values_heading = models.CharField(
        max_length=255, verbose_name="Заглавие на ценности"
    )
    our_values_subheading = models.CharField(
        max_length=255, verbose_name="Подзаглавие на ценности"
    )

    our_team_heading = models.CharField(
        max_length=255, verbose_name="Заглавие на командата"
    )
    our_team_subheading = models.CharField(
        max_length=255, verbose_name="Подзаглавие на командата"
    )

    our_team_heading = models.CharField(
        max_length=255, verbose_name="Заглавие на нашия екип"
    )
    our_team_subheading = models.CharField(
        max_length=255, verbose_name="Подзаглавие на нашия екип"
    )

    cta_heading = models.CharField(
        max_length=255, verbose_name="Заглавие на CTA секцията"
    )
    cta_subheading = models.CharField(
        max_length=255, verbose_name="Подзаглавие на CTA секцията"
    )

    class Meta:
        verbose_name = "Страница За Нас"
        verbose_name_plural = "Страница За Нас"

    def __str__(self):
        return self.heading


class ValueCard(models.Model):
    about_page = models.ForeignKey(
        SiteAboutUs,
        on_delete=models.CASCADE,
        related_name="value_cards",
        verbose_name="Страница",
    )
    icon = models.CharField(
        max_length=100,
        verbose_name="Икона",
        help_text="Клас на иконата (например: 'fa-solid fa-heart') или SVG път",
    )
    title = models.CharField(max_length=100, verbose_name="Заглавие")
    description = models.TextField(verbose_name="Текст")
    order = models.PositiveIntegerField(default=0, verbose_name="Поредност")

    class Meta:
        verbose_name = "Картичка с ценност"
        verbose_name_plural = "Картички с ценности"
        ordering = ["order"]

    def __str__(self):
        return self.title


class ContactPage(models.Model):
    heading = models.CharField(max_length=255, verbose_name="Заглавие")
    subheading = models.CharField(max_length=255, verbose_name="Подзаглавие")

    contact_heading = models.CharField(max_length=255, verbose_name="Заглавие Контакт")

    send_message_heading = models.CharField(
        max_length=255, verbose_name="Заглавие Съобщение"
    )
    send_message_subheading = models.CharField(
        max_length=255, verbose_name="Подзаглавие Съобщение"
    )

    map_heading = models.CharField(max_length=255, verbose_name="Заглавие Карта")
    map_subheading = models.CharField(max_length=255, verbose_name="Подзаглавие Карта")

    class Meta:
        verbose_name = "Страница Контакти"
        verbose_name_plural = "Страница Контакти"

    def __str__(self):
        return self.heading


class PrivacyPolicy(models.Model):
    text = models.TextField(verbose_name="Текст")

    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата на създаване"
    )
    updated_at = models.DateTimeField(
        auto_now=True, verbose_name="Дата на последно обновяване"
    )

    class Meta:
        verbose_name = "Политика за поверителност"
        verbose_name_plural = "Политика за поверителност"

    def __str__(self):
        return "Политика за поверителност"


class TermsOfService(models.Model):
    text = models.TextField(verbose_name="Текст")

    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name="Дата на създаване"
    )
    updated_at = models.DateTimeField(
        auto_now=True, verbose_name="Дата на последно обновяване"
    )

    class Meta:
        verbose_name = "Общи условия"
        verbose_name_plural = "Общи условия"

    def __str__(self):
        return "Общи условия"
