from django.middleware.locale import LocaleMiddleware
from django.utils import translation


class AdminLocaleMiddleware(LocaleMiddleware):
    def __call__(self, request):
        if request.path.startswith('/admin/'):
            # For admin paths, force Bulgarian language and bypass parent logic.
            language = 'bg'
            translation.activate(language)
            request.LANGUAGE_CODE = translation.get_language()

            # Get response from the next middleware in the chain
            response = self.get_response(request)

            # Set Content-Language header on the response
            response.headers.setdefault('Content-Language', language)
            return response
        else:
            # For all other paths, use the default LocaleMiddleware behavior.
            return super().__call__(request)
