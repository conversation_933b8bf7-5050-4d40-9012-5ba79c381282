# Django Management Commands - Тестови данни

Този модул съдържа Django команди за управление на тестови данни в приложението за имоти.

## Налични команди

### 1. `create_test_properties` - Създаване на тестови имоти

Създава тестови данни за имоти заедно с всички необходими зависимости (локации, типове имоти, екстри, брокери).

#### Използване:

```bash
# Създаване на 20 имота (по подразбиране)
python manage.py create_test_properties

# Създаване на определен брой имоти
python manage.py create_test_properties --count=50

# Помощ за командата
python manage.py create_test_properties --help
```

#### Какво създава командата:

**Основни данни (ако не съществуват):**

- 6 града: София, Пловдив, Варна, Бургас, Русе, Стара Загора
- 39 квартала в различните градове
- 9 типа имоти: Апартамент, Къща, Вила, Студио, Мезонет, Офис, Магазин, Гараж, Парцел
- 20 екстри: Асансьор, Гараж, Балкон, Тераса, и др.
- 5 брокера с пълни данни

**Тестови имоти:**

- Случайно генерирани заглавия и описания
- Реалистични цени спрямо локацията и площта
- Случайни екстри (2-6 за всеки имот)
- 75% публикувани, 25% препоръчани
- Уникални slug-ове за всеки имот

### 2. `clear_test_data` - Изчистване на тестови данни

Изтрива всички имоти и свързани данни от базата.

#### Използване:

```bash
# Показва предупреждение и инструкции
python manage.py clear_test_data

# Изпълнява изчистването (ВНИМАНИЕ: необратимо!)
python manage.py clear_test_data --confirm
```

#### Какво изтрива:

- Всички имоти (Property)
- Всички снимки на имоти (PropertyImage)
- Всички запитвания за контакт (ContactInquiry)

#### Какво НЕ изтрива:

- Локации (Location)
- Типове имоти (PropertyType)
- Екстри (Feature)
- Брокери (TeamMember)

Това позволява лесно пресъздаване на тестови имоти без да се загубят основните референтни данни.

## Примери за използване

### Пълно пресъздаване на тестови данни:

```bash
# 1. Изчистване на старите данни
python manage.py clear_test_data --confirm

# 2. Създаване на нови тестови данни
python manage.py create_test_properties --count=30
```

### Добавяне на още тестови данни:

```bash
# Добавя още 10 имота към съществуващите
python manage.py create_test_properties --count=10
```

### Проверка на статистиката:

```bash
python manage.py shell -c "
from core.models import Property, Location, PropertyType, Feature, TeamMember
print(f'Имоти: {Property.objects.count()}')
print(f'Локации: {Location.objects.count()}')
print(f'Типове имоти: {PropertyType.objects.count()}')
print(f'Екстри: {Feature.objects.count()}')
print(f'Брокери: {TeamMember.objects.count()}')
"
```

## Забележки

- Командите автоматично създават всички необходими зависимости
- Slug-овете се генерират автоматично и са уникални
- Цените се формират реалистично спрямо локацията и площта
- Всички данни са на български език
- Командите могат да се изпълняват многократно без проблеми
