import random
from django.core.management.base import BaseCommand

from core.models import (
    HeroSection,
    FeaturedPropertiesSection,
    WhyUsSection,
    WhyUsSectionCard,
    CTASection,
    SiteAboutUs,
    ValueCard,
    ContactPage,
    PrivacyPolicy,
    TermsOfService,
)
from siteconfig.models import SiteSettings


class Command(BaseCommand):
    help = "Създава тестови секции за началната страница (Hero, Featured, Why Us, CTA)"

    def handle(self, *args, **options):
        self.stdout.write("Започвам създаването на тестови секции...")

        self.create_site_settings()
        self.create_hero_section()
        self.create_featured_properties_section()
        self.create_why_us_section()
        self.create_cta_section()
        self.create_about_us_section()
        self.create_contact_page()
        self.create_privacy_policy()
        self.create_terms_of_service()

        self.stdout.write(
            self.style.SUCCESS("Всички секции са създадени / актуализирани успешно.")
        )

    # ------------------------------------------------------------------
    # Helper methods
    # ------------------------------------------------------------------

    def create_site_settings(self):
        """Създава или обновява настройките на сайта"""
        data = {
            "name": "Имоти България",
            "description": "Водещата агенция за недвижими имоти в България. Предлагаме професионални услуги за покупка, продажба и наем на имоти.",
            "phone": "+359 888 123 456",
            "email": "<EMAIL>",
            "street": "ул. Витоша 15",
            "city": "София",
            "working_hours": "Понеделник - Петък: 9:00 - 18:00\nСъбота: 10:00 - 16:00\nНеделя: Почивен ден",
            "facebook_url": "https://facebook.com/imoti-bulgaria",
            "youtube_url": "https://youtube.com/@imoti-bulgaria",
            "instagram_url": "https://instagram.com/imoti_bulgaria",
        }
        obj, created = SiteSettings.objects.update_or_create(defaults=data)
        self._log_create_or_update(obj, created, "Настройки на сайта")

    def create_hero_section(self):
        """Създава или обновява Hero секция"""
        data = {
            "heading": "Намерете мечтания си дом",
            "subheading": "Най-добрите имоти в България",
            "is_active": True,
        }
        obj, created = HeroSection.objects.update_or_create(pk=1, defaults=data)
        self._log_create_or_update(obj, created)

    def create_featured_properties_section(self):
        data = {
            "heading": "Препоръчани имоти",
            "subheading": "Подбрани оферти за вас",
            "is_active": True,
        }
        obj, created = FeaturedPropertiesSection.objects.update_or_create(
            pk=1, defaults=data
        )
        self._log_create_or_update(obj, created)

    def create_why_us_section(self):
        section_data = {
            "heading": "Защо да изберете нас?",
            "subheading": "Ние предлагаме качество, прозрачност и опит",
            "is_active": True,
        }
        section, created = WhyUsSection.objects.update_or_create(
            pk=1, defaults=section_data
        )
        self._log_create_or_update(section, created)

        # -- Cards ------------------------------------------------------
        cards_data = [
            {
                "title": "Професионализъм",
                "description": "Нашият екип е съставен от опитни експерти на пазара на имоти.",
                "icon": "briefcase",
                "order": 1,
            },
            {
                "title": "Прозрачност",
                "description": "Ясни условия и честна комуникация с всеки клиент.",
                "icon": "file-alt",
                "order": 2,
            },
            {
                "title": "Подкрепа",
                "description": "Ние сме с вас през целия процес – от огледа до финализирането.",
                "icon": "hands-helping",
                "order": 3,
            },
        ]

        for card_data in cards_data:
            card_data["section"] = section
            card, created_card = WhyUsSectionCard.objects.update_or_create(
                section=section, order=card_data["order"], defaults=card_data
            )
            self._log_create_or_update(card, created_card, prefix="  └ Картичка")

    def create_cta_section(self):
        data = {
            "heading": "Свържете се с нас днес",
            "subheading": "Ние сме готови да помогнем при всяка стъпка",
            "is_active": True,
        }
        obj, created = CTASection.objects.update_or_create(pk=1, defaults=data)
        self._log_create_or_update(obj, created)

    def create_about_us_section(self):
        """Създава или обновява About Us секция"""
        data = {
            "heading": "За нас",
            "subheading": "Вашият надежден партньор в света на недвижимите имоти",
            "our_history_heading": "Нашата история",
            "our_history_content": "От основаването си преди повече от 10 години, ние сме се утвърдили като една от водещите агенции за недвижими имоти в България. Започнахме като малък семеен бизнес с мечтата да помагаме на хората да намерят своя идеален дом.",
            "our_values_heading": "Нашите ценности",
            "our_values_subheading": "Принципите, които ни водят всеки ден",
            "our_team_heading": "Нашият екип",
            "our_team_subheading": "Професионалисти, които се грижат за вашите нужди",
            "cta_heading": "Готови сте да започнете?",
            "cta_subheading": "Свържете се с нас днес и нека ви помогнем да намерите мечтания си дом",
        }
        obj, created = SiteAboutUs.objects.update_or_create(pk=1, defaults=data)
        self._log_create_or_update(obj, created, "About Us секция")

        # Създаване на value cards
        value_cards_data = [
            {
                "icon": "fa-solid fa-heart",
                "title": "Страст",
                "description": "Ние обичаме това, което правим и се стремим към съвършенство във всеки проект.",
                "order": 1,
            },
            {
                "icon": "fa-solid fa-handshake",
                "title": "Доверие",
                "description": "Изграждаме дългосрочни отношения, базирани на взаимно доверие и прозрачност.",
                "order": 2,
            },
            {
                "icon": "fa-solid fa-star",
                "title": "Качество",
                "description": "Предлагаме само най-добрите имоти и услуги на най-високо ниво.",
                "order": 3,
            },
        ]

        for card_data in value_cards_data:
            card_data["about_page"] = obj
            card, created_card = ValueCard.objects.update_or_create(
                about_page=obj, order=card_data["order"], defaults=card_data
            )
            self._log_create_or_update(card, created_card, prefix="  └ Value Card")

    def create_contact_page(self):
        """Създава или обновява Contact страница"""
        data = {
            "heading": "Свържете се с нас",
            "subheading": "Ние сме тук, за да отговорим на всички ваши въпроси",
            "contact_heading": "Контактна информация",
            "send_message_heading": "Изпратете ни съобщение",
            "send_message_subheading": "Попълнете формата по-долу и ние ще се свържем с вас възможно най-скоро",
            "map_heading": "Намерете ни",
            "map_subheading": "Посетете нашия офис в центъра на София",
        }
        obj, created = ContactPage.objects.update_or_create(pk=1, defaults=data)
        self._log_create_or_update(obj, created, "Contact страница")

    def create_privacy_policy(self):
        """Създава или обновява политика за поверителност"""
        text = """# Политика за поверителност

## 1. Обща информация

Тази политика за поверителност описва как събираме, използваме и защитаваме вашата лична информация.

## 2. Събиране на информация

Ние събираме информация, която ни предоставяте доброволно чрез:
- Контактни форми
- Телефонни разговори
- Имейл кореспонденция
- Посещения в офиса ни

## 3. Използване на информацията

Вашата информация се използва за:
- Предоставяне на услуги
- Комуникация с вас
- Подобряване на нашите услуги

## 4. Защита на данните

Ние предприемаме всички необходими мерки за защита на вашите лични данни.

## 5. Контакт

За въпроси относно тази политика, моля свържете се с нас на <EMAIL>
"""
        obj, created = PrivacyPolicy.objects.update_or_create(defaults={"text": text})
        self._log_create_or_update(obj, created, "Политика за поверителност")

    def create_terms_of_service(self):
        """Създава или обновява условия за ползване"""
        text = """# Условия за ползване

## 1. Приемане на условията

Използвайки нашия уебсайт и услуги, вие се съгласявате с тези условия.

## 2. Използване на услугите

Нашите услуги са предназначени за:
- Търсене на имоти
- Получаване на консултации
- Свързване с нашия екип

## 3. Отговорности на потребителя

Потребителят се задължава да:
- Предоставя точна информация
- Не злоупотребява с услугите
- Спазва приложимото законодателство

## 4. Ограничения на отговорността

Ние не носим отговорност за:
- Непреки щети
- Загуба на печалба
- Прекъсване на дейността

## 5. Изменения

Запазваме си правото да променяме тези условия по всяко време.

## 6. Контакт

За въпроси относно тези условия, моля свържете се с нас на <EMAIL>
"""
        obj, created = TermsOfService.objects.update_or_create(defaults={"text": text})
        self._log_create_or_update(obj, created, "Условия за ползване")

    # ------------------------------------------------------------------
    def _log_create_or_update(
        self, obj, created: bool, prefix: str = "", name: str = ""
    ):
        action = "Създадена" if created else "Обновена"
        display_name = name if name else str(obj)
        self.stdout.write(f"{prefix}{action}: {display_name}")
