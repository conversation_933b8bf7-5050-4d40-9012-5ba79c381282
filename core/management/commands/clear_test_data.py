from django.core.management.base import BaseCommand
from properties.models import (
    Property,
    PropertyImage,
    PropertyVideo,
    City,
    Location,
    PropertyType,
    Feature,
)
from core.models import (
    ContactInquiry,
    TeamMember,
    HeroSection,
    FeaturedPropertiesSection,
    WhyUsSection,
    WhyUsSectionCard,
    CTASection,
    SiteAboutUs,
    ValueCard,
    ContactPage,
    PrivacyPolicy,
    TermsOfService,
)
from siteconfig.models import SiteSettings


class Command(BaseCommand):
    help = "Изчиства всички данни от базата данни"

    def add_arguments(self, parser):
        parser.add_argument(
            "--confirm",
            action="store_true",
            help="Потвърдете, че искате да изчистите всички данни",
        )
        parser.add_argument(
            "--preserve-cities",
            action="store_true",
            help="Запазва данните за градове, локации, типове имоти, екстри и екип",
        )
        parser.add_argument(
            "--preserve-sections",
            action="store_true",
            help="Запазва данните за секциите на сайта и настройките",
        )

    def handle(self, *args, **options):
        if not options["confirm"]:
            self.stdout.write(
                self.style.WARNING(
                    "ВНИМАНИЕ: Тази команда ще изтрие всички данни от базата!\n"
                    "Това включва:\n"
                    "- Всички имоти, техните снимки и видеа\n"
                    "- Всички запитвания от клиенти\n"
                    "- Всички отзиви\n"
                    "- Всички градове и локации\n"
                    "- Всички типове имоти и техните характеристики\n"
                    "- Всички членове на екипа\n"
                    "- Всички секции на сайта (Hero, Featured, Why Us, CTA, About)\n"
                    "- Настройките на сайта\n"
                    "- Политики за поверителност и условия за ползване\n\n"
                    "За да потвърдите, изпълнете командата с --confirm флаг:\n"
                    "python manage.py clear_test_data --confirm\n\n"
                    "Ако искате да запазите градове, локации, типове имоти, екстри и екип, използвайте:\n"
                    "python manage.py clear_test_data --confirm --preserve-cities\n\n"
                    "Ако искате да запазите и секциите на сайта и настройките, използвайте:\n"
                    "python manage.py clear_test_data --confirm --preserve-cities --preserve-sections"
                    )
                )
            )
            return

        self.stdout.write("Започвам изчистването на данните...")

        # Броене на записите преди изтриване
        properties_count = Property.objects.count()
        images_count = PropertyImage.objects.count()
        videos_count = PropertyVideo.objects.count()
        inquiries_count = ContactInquiry.objects.count()
        cities_count = City.objects.count()
        locations_count = Location.objects.count()
        property_types_count = PropertyType.objects.count()
        features_count = Feature.objects.count()
        team_members_count = TeamMember.objects.count()

        # Броене на секции и настройки
        hero_sections_count = HeroSection.objects.count()
        featured_sections_count = FeaturedPropertiesSection.objects.count()
        why_us_sections_count = WhyUsSection.objects.count()
        why_us_cards_count = WhyUsSectionCard.objects.count()
        cta_sections_count = CTASection.objects.count()
        about_pages_count = SiteAboutUs.objects.count()
        value_cards_count = ValueCard.objects.count()
        contact_pages_count = ContactPage.objects.count()
        privacy_policies_count = PrivacyPolicy.objects.count()
        terms_count = TermsOfService.objects.count()
        site_settings_count = SiteSettings.objects.count()

        # Изтриване на основните данни
        PropertyVideo.objects.all().delete()
        PropertyImage.objects.all().delete()
        ContactInquiry.objects.all().delete()
        Property.objects.all().delete()

        # Изтриване на секции и настройки, освен ако не е зададено да се запазят
        if not options["preserve_sections"]:
            ValueCard.objects.all().delete()
            WhyUsSectionCard.objects.all().delete()
            HeroSection.objects.all().delete()
            FeaturedPropertiesSection.objects.all().delete()
            WhyUsSection.objects.all().delete()
            CTASection.objects.all().delete()
            SiteAboutUs.objects.all().delete()
            ContactPage.objects.all().delete()
            PrivacyPolicy.objects.all().delete()
            TermsOfService.objects.all().delete()
            SiteSettings.objects.all().delete()

        # Изтриване на допълнителните данни, освен ако не е зададено да се запазят
        if not options["preserve_cities"]:
            Location.objects.all().delete()
            City.objects.all().delete()
            PropertyType.objects.all().delete()
            Feature.objects.all().delete()
            TeamMember.objects.all().delete()

        # Подготвяне на съобщението с резултатите
        result_message = (
            f"Успешно изтрити:\n"
            f"- {properties_count} имота\n"
            f"- {images_count} снимки\n"
            f"- {videos_count} видеа\n"
            f"- {inquiries_count} запитвания\n"
        )

        if not options["preserve_sections"]:
            result_message += (
                f"\n- {hero_sections_count} hero секции\n"
                f"- {featured_sections_count} featured секции\n"
                f"- {why_us_sections_count} why us секции\n"
                f"- {why_us_cards_count} why us карти\n"
                f"- {cta_sections_count} CTA секции\n"
                f"- {about_pages_count} about страници\n"
                f"- {value_cards_count} value карти\n"
                f"- {contact_pages_count} контакт страници\n"
                f"- {privacy_policies_count} политики за поверителност\n"
                f"- {terms_count} условия за ползване\n"
                f"- {site_settings_count} настройки на сайта"
            )

        if not options["preserve_cities"]:
            result_message += (
                f"\n- {cities_count} града\n"
                f"- {locations_count} локации\n"
                f"- {property_types_count} типа имоти\n"
                f"- {features_count} екстри\n"
                f"- {team_members_count} членове на екипа"
            )

        # Добавяне на информация за запазените данни
        preserved_items = []
        if options["preserve_cities"]:
            preserved_items.extend(
                [
                    "- Градове и локации",
                    "- Типове имоти",
                    "- Екстри",
                    "- Членове на екипа",
                ]
            )
        if options["preserve_sections"]:
            preserved_items.extend(
                ["- Секции на сайта", "- Настройки на сайта", "- Политики и условия"]
            )

        if preserved_items:
            result_message += "\n\nЗапазени са:\n" + "\n".join(preserved_items)

        self.stdout.write(self.style.SUCCESS(result_message))
