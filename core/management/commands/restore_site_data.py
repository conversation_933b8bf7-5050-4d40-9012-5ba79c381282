import json
from django.core.management.base import BaseCommand
from django.db import transaction

from core.models import (
    TeamMember,
    HeroSection,
    FeaturedPropertiesSection,
    WhyUsSection,
    WhyUsSectionCard,
    CTASection,
    SiteAboutUs,
    ValueCard,
    ContactPage,
    PrivacyPolicy,
    TermsOfService,
)
from siteconfig.models import SiteSettings


class Command(BaseCommand):
    help = "Възстановява данни от backup файл създаден с backup_site_data командата"

    def add_arguments(self, parser):
        parser.add_argument(
            "backup_file",
            type=str,
            help="Път към backup файла",
        )
        parser.add_argument(
            "--clear-existing",
            action="store_true",
            help="Изтрива съществуващите данни преди възстановяване",
        )
        parser.add_argument(
            "--confirm",
            action="store_true",
            help="Потвърждава операцията без допълнителни въпроси",
        )

    def handle(self, *args, **options):
        backup_file = options["backup_file"]
        clear_existing = options["clear_existing"]
        confirm = options["confirm"]

        if not confirm:
            self.stdout.write(
                self.style.WARNING(
                    "ВНИМАНИЕ: Тази команда ще възстанови данни от backup файл!\n"
                    "Това може да промени или замени съществуващи данни.\n\n"
                    "За да потвърдите, изпълнете командата с --confirm флаг:\n"
                    f"python manage.py restore_site_data {backup_file} --confirm\n\n"
                    "Ако искате да изтриете съществуващите данни преди възстановяване:\n"
                    f"python manage.py restore_site_data {backup_file} --confirm --clear-existing"
                )
            )
            return

        # Зареждане на backup данните
        try:
            with open(backup_file, "r", encoding="utf-8") as f:
                backup_data = json.load(f)
        except FileNotFoundError:
            self.stdout.write(
                self.style.ERROR(f"❌ Файлът {backup_file} не е намерен!")
            )
            return
        except json.JSONDecodeError as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Грешка при четене на JSON файла: {e}")
            )
            return

        if "data" not in backup_data:
            self.stdout.write(
                self.style.ERROR("❌ Невалиден backup файл - липсва 'data' секция!")
            )
            return

        data = backup_data["data"]
        self.stdout.write("Започвам възстановяването на данни...")

        try:
            with transaction.atomic():
                # Изтриване на съществуващи данни ако е заявено
                if clear_existing:
                    self.stdout.write("Изтриване на съществуващи данни...")
                    self._clear_existing_data()

                # Възстановяване на SiteSettings
                if data.get("site_settings"):
                    self._restore_site_settings(data["site_settings"])

                # Възстановяване на TeamMembers
                if data.get("team_members"):
                    self._restore_team_members(data["team_members"])

                # Възстановяване на HeroSection
                if data.get("hero_section"):
                    self._restore_hero_section(data["hero_section"])

                # Възстановяване на FeaturedPropertiesSection
                if data.get("featured_properties_section"):
                    self._restore_featured_properties_section(
                        data["featured_properties_section"]
                    )

                # Възстановяване на WhyUsSection
                if data.get("why_us_section"):
                    self._restore_why_us_section(data["why_us_section"])

                # Възстановяване на CTASection
                if data.get("cta_section"):
                    self._restore_cta_section(data["cta_section"])

                # Възстановяване на SiteAboutUs
                if data.get("site_about_us"):
                    self._restore_site_about_us(data["site_about_us"])

                # Възстановяване на ContactPage
                if data.get("contact_page"):
                    self._restore_contact_page(data["contact_page"])

                # Възстановяване на PrivacyPolicy
                if data.get("privacy_policy"):
                    self._restore_privacy_policy(data["privacy_policy"])

                # Възстановяване на TermsOfService
                if data.get("terms_of_service"):
                    self._restore_terms_of_service(data["terms_of_service"])

                self.stdout.write(
                    self.style.SUCCESS(
                        f"\n✅ Възстановяването завърши успешно!\n"
                        f"Данните от {backup_file} са заредени в базата данни."
                    )
                )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Грешка при възстановяване: {e}"))
            raise

    def _clear_existing_data(self):
        """Изтрива съществуващите данни"""
        ValueCard.objects.all().delete()
        WhyUsSectionCard.objects.all().delete()
        HeroSection.objects.all().delete()
        FeaturedPropertiesSection.objects.all().delete()
        WhyUsSection.objects.all().delete()
        CTASection.objects.all().delete()
        SiteAboutUs.objects.all().delete()
        ContactPage.objects.all().delete()
        PrivacyPolicy.objects.all().delete()
        TermsOfService.objects.all().delete()
        TeamMember.objects.all().delete()
        SiteSettings.objects.all().delete()
        self.stdout.write(self.style.SUCCESS("✓ Съществуващите данни са изтрити"))

    def _restore_site_settings(self, data):
        """Възстановява настройките на сайта"""
        obj, created = SiteSettings.objects.update_or_create(defaults=data)
        action = "Създадени" if created else "Обновени"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} настройки на сайта"))

    def _restore_team_members(self, data):
        """Възстановява членовете на екипа"""
        count = 0
        for member_data in data:
            obj, created = TeamMember.objects.get_or_create(
                email=member_data["email"], defaults=member_data
            )
            if created:
                count += 1
        self.stdout.write(
            self.style.SUCCESS(f"✓ Възстановени {count} членове на екипа")
        )

    def _restore_hero_section(self, data):
        """Възстановява Hero секцията"""
        obj, created = HeroSection.objects.update_or_create(pk=1, defaults=data)
        action = "Създадена" if created else "Обновена"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} Hero секция"))

    def _restore_featured_properties_section(self, data):
        """Възстановява Featured Properties секцията"""
        obj, created = FeaturedPropertiesSection.objects.update_or_create(
            pk=1, defaults=data
        )
        action = "Създадена" if created else "Обновена"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} Featured Properties секция"))

    def _restore_why_us_section(self, data):
        """Възстановява Why Us секцията и картите"""
        cards_data = data.pop("cards", [])
        obj, created = WhyUsSection.objects.update_or_create(pk=1, defaults=data)
        action = "Създадена" if created else "Обновена"

        # Възстановяване на картите
        cards_count = 0
        for card_data in cards_data:
            card_data["section"] = obj
            card_obj, card_created = WhyUsSectionCard.objects.update_or_create(
                section=obj, order=card_data["order"], defaults=card_data
            )
            if card_created:
                cards_count += 1

        self.stdout.write(
            self.style.SUCCESS(f"✓ {action} Why Us секция с {cards_count} карти")
        )

    def _restore_cta_section(self, data):
        """Възстановява CTA секцията"""
        obj, created = CTASection.objects.update_or_create(pk=1, defaults=data)
        action = "Създадена" if created else "Обновена"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} CTA секция"))

    def _restore_site_about_us(self, data):
        """Възстановява About Us секцията и value картите"""
        value_cards_data = data.pop("value_cards", [])
        obj, created = SiteAboutUs.objects.update_or_create(pk=1, defaults=data)
        action = "Създадена" if created else "Обновена"

        # Възстановяване на value картите
        cards_count = 0
        for card_data in value_cards_data:
            card_data["about_page"] = obj
            card_obj, card_created = ValueCard.objects.update_or_create(
                about_page=obj, order=card_data["order"], defaults=card_data
            )
            if card_created:
                cards_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"✓ {action} About Us секция с {cards_count} value карти"
            )
        )

    def _restore_contact_page(self, data):
        """Възстановява Contact страницата"""
        obj, created = ContactPage.objects.update_or_create(pk=1, defaults=data)
        action = "Създадена" if created else "Обновена"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} Contact страница"))

    def _restore_privacy_policy(self, data):
        """Възстановява политиката за поверителност"""
        obj, created = PrivacyPolicy.objects.update_or_create(defaults=data)
        action = "Създадена" if created else "Обновена"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} политика за поверителност"))

    def _restore_terms_of_service(self, data):
        """Възстановява условията за ползване"""
        obj, created = TermsOfService.objects.update_or_create(defaults=data)
        action = "Създадени" if created else "Обновени"
        self.stdout.write(self.style.SUCCESS(f"✓ {action} условия за ползване"))
