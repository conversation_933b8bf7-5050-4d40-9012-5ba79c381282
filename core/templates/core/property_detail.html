{% extends 'core/base.html' %}
{% load static l10n i18n %}

{% block title %}
    {{ property.title }} - {{ COMPANY.name }}
{% endblock title %}

{% block content %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'core:home' %}"
                       class="text-on-surface-variant hover:text-primary">{% trans "Начало" %}</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-on-surface-variant mx-1"
                             fill="currentColor"
                             viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd">
                            </path>
                        </svg>
                        <a href="{% url 'core:property-list' %}"
                           class="text-on-surface-variant hover:text-primary">{% trans "Имоти" %}</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-on-surface-variant mx-1"
                             fill="currentColor"
                             viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd">
                            </path>
                        </svg>
                        <span class="text-on-surface">{{ property.title }}</span>
                    </div>
                </li>
            </ol>
        </nav>

        <div class="lg:grid lg:grid-cols-3 lg:gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Property Header -->
                <div class="mb-8">
                    <div class="flex items-start justify-between mb-4">
                        <div>
                            <h1 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-2">{{ property.title }}</h1>
                            <div class="flex items-center text-on-surface-variant mb-4">
                                <span>{{ property.property_type.name }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ property.location.name }}, {{ property.location.city.name }}</span>
                            </div>
                        </div>

                        {% if property.badge %}
                            <span class="bg-accent text-on-accent px-3 py-1 rounded-radius text-sm font-medium">{{ property.badge }}</span>
                        {% endif %}
                    </div>

                    <!-- Key Properties -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-surface-variant rounded-radius p-4 text-center">
                            <div class="font-bold text-2xl text-primary">
                                {{ property.price|floatformat:"0"|localize }} {{ property.currency }}
                            </div>
                            <div class="text-sm text-on-surface-variant">{% trans "Цена" %}</div>
                        </div>
                        <div class="bg-surface-variant rounded-radius p-4 text-center">
                            <div class="font-bold text-2xl text-on-surface">
                                {{ property.area|floatformat:"0"|localize }} <span class="text-base">m<sup>2</sup></span>
                            </div>
                            <div class="text-sm text-on-surface-variant">{% trans "площ" %}</div>
                        </div>
                        {% if property.rooms > 0 %}
                            <div class="bg-surface-variant rounded-radius p-4 text-center">
                                <div class="font-bold text-2xl text-on-surface">{{ property.rooms }}</div>
                                <div class="text-sm text-on-surface-variant">{% trans "стаи" %}</div>
                            </div>
                        {% endif %}
                        <div class="bg-surface-variant rounded-radius p-4 text-center">
                            <div class="font-bold text-xl text-on-surface">{{ property.created_at|date:"SHORT_DATE_FORMAT" }}</div>
                            <div class="text-sm text-on-surface-variant">{% trans "публикувано" %}</div>
                        </div>
                    </div>
                </div>

                <div class="my-8">{% include "partials/_property_gallery.html" %}</div>

                <!-- Description -->
                <div class="mb-8">
                    <h2 class="font-title text-2xl font-bold text-on-surface mb-4">{% trans "Описание" %}</h2>
                    <div class="prose max-w-none text-on-surface">{{ property.description|linebreaks }}</div>
                </div>

                <!-- Features -->
                {% if property.features.all %}
                    <div class="mb-8">
                        <h2 class="font-title text-2xl font-bold text-on-surface mb-4">{% trans "Екстри" %}</h2>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            {% for feature in property.features.all %}
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-primary mr-2"
                                         fill="none"
                                         stroke="currentColor"
                                         viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-on-surface">{{ feature.name }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                <!-- Map placeholder -->
                <div class="mb-8">
                    <h2 class="font-title text-2xl font-bold text-on-surface mb-4">{% trans "Местоположение" %}</h2>
                    <div class="w-full aspect-[3/2] bg-surface-variant rounded-radius flex items-center justify-center">
                        {% if property.location.google_map_url %}
                            <iframe src="{{ property.location.google_map_url }}"
                                    allowfullscreen=""
                                    loading="lazy"
                                    referrerpolicy="no-referrer-when-downgrade"
                                    class="w-full h-full"></iframe>
                        {% else %}
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-on-surface-variant"
                                     fill="none"
                                     stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <p class="mt-2 text-on-surface-variant">{{ property.location }}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 mt-8 lg:mt-0">
                <!-- Contact Card -->
                <div class="bg-surface border border-outline rounded-radius p-6 sticky top-24">
                    <!-- Price -->
                    <div class="text-center mb-6">
                        <div class="text-3xl font-bold text-primary mb-2">
                            {{ property.price|floatformat:"0"|localize }} {{ property.currency }}
                        </div>
                        <div class="text-sm text-on-surface-variant">ID: {{ property.id }}</div>
                    </div>

                    <!-- Broker Info -->
                    <div class="flex items-center mb-6 p-4 bg-surface-variant rounded-radius">
                        {% if property.assigned_broker.photo %}
                            <img src="{{ property.assigned_broker.photo.url }}"
                                 alt="{{ property.assigned_broker.name }}"
                                 class="w-16 h-16 rounded-full object-cover mr-4">
                        {% else %}
                            {% comment %} <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mr-4">
                                <span class="text-2xl font-bold text-on-primary">{{ property.assigned_broker.name|first }}</span>
                            </div> {% endcomment %}
                            <div class="mr-4 flex size-16 items-center justify-center overflow-hidden rounded-full border border-outline bg-surface-alt text-on-surface/50 dark:border-outline-dark dark:bg-surface-dark-alt dark:text-on-surface-dark/50">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                     viewBox="0 0 24 24"
                                     aria-hidden="true"
                                     fill="currentColor"
                                     class="w-full h-full mt-3">
                                    <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        {% endif %}
                        <div>
                            <h3 class="font-semibold text-on-surface">{{ property.assigned_broker.name }}</h3>
                            <p class="text-sm text-on-surface-variant mb-1">{{ property.assigned_broker.title }}</p>
                            <p class="text-sm text-primary font-medium">{{ property.assigned_broker.phone }}</p>
                        </div>
                    </div>

                    <!-- Contact Form -->
                    <form x-data="contactForm()"
                          @submit.prevent="submitForm()"
                          class="space-y-4">
                        <input type="hidden" name="property_id" value="{{ property.id }}">

                        <div>
                            <label for="name" class="block text-sm font-medium text-on-surface mb-1">{% trans "Име *" %}</label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   autocomplete="name"
                                   x-model="form.name"
                                   required
                                   class="w-full px-3 py-2 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-on-surface mb-1">{% trans "Имейл *" %}</label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   autocomplete="email"
                                   x-model="form.email"
                                   required
                                   class="w-full px-3 py-2 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-on-surface mb-1">{% trans "Телефон" %}</label>
                            <input type="tel"
                                   id="phone"
                                   name="phone"
                                   autocomplete="tel"
                                   x-model="form.phone"
                                   class="w-full px-3 py-2 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium text-on-surface mb-1">{% trans "Съобщение *" %}</label>
                            <textarea id="message"
                                      name="message"
                                      autocomplete="off"
                                      x-model="form.message"
                                      rows="4"
                                      required
                                      placeholder="{% trans "Здравейте, интересувам се от този имот..." %}"
                                      class="w-full px-3 py-2 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                        </div>

                        <button type="submit"
                                :disabled="loading"
                                :class="loading ? 'opacity-50 cursor-not-allowed' : ''"
                                class="btn btn-primary w-full">
                            <span x-show="!loading">{% trans "Изпрати запитване" %}</span>
                            <span x-show="loading" class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-on-primary"
                                     fill="none"
                                     viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                {% trans "Изпраща..." %}
                            </span>
                        </button>

                        <!-- Success Message -->
                        <div x-show="success"
                             x-transition
                             class="p-4 bg-success/20 border border-success/30 rounded-radius">
                            <p class="text-success text-sm">{% trans "Запитването е изпратено успешно! Ще се свържем с вас скоро." %}</p>
                        </div>

                        <!-- Error Message -->
                        <div x-show="error"
                             x-transition
                             class="p-4 bg-danger/20 border border-danger/30 rounded-radius">
                            <p class="text-danger text-sm" x-text="errorMessage"></p>
                        </div>
                    </form>

                    <script>
                document.addEventListener('alpine:init', () => {
                    Alpine.data('contactForm', () => ({
                        form: {
                            name: '',
                            email: '',
                            phone: '',
                            message: '{% trans "Здравейте, интересувам се от този имот. Моля за повече информация." %}'
                        },
                        loading: false,
                        success: false,
                        error: false,
                        errorMessage: '',

                        async submitForm() {
                            this.loading = true
                            this.success = false
                            this.error = false

                            try {
                                const formData = new FormData()
                                formData.append('name', this.form.name)
                                formData.append('email', this.form.email)
                                formData.append('phone', this.form.phone)
                                formData.append('message', this.form.message)
                                formData.append('property_id', '{{ property.id }}')

                                const response = await fetch('{% url "core:contact-inquiry-create" %}', {
                                    method: 'POST',
                                    body: formData,
                                    headers: {
                                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                                    }
                                })

                                const data = await response.json()

                                if (data.success) {
                                    this.success = true
                                    this.form = {
                                        name: '',
                                        email: '',
                                        phone: '',
                                        message: '{% trans "Здравейте, интересувам се от този имот. Моля за повече информация." %}'
                                    }
                                } else {
                                    this.error = true
                                    this.errorMessage = data.error || '{% trans "Възникна грешка при изпращане на запитването." %}'
                                }
                            } catch (err) {
                                this.error = true
                                this.errorMessage = '{% trans "Възникна грешка при изпращане на запитването." %}'
                            } finally {
                                this.loading = false
                            }
                        }
                    }))
                })
                    </script>
                </div>
            </div>
        </div>

        <!-- Similar Properties -->
        {% if similar_properties %}
            <div class="mt-16">
                <h2 class="font-title text-3xl font-bold text-on-surface mb-8">{% trans "Подобни оферти" %}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {% for property in similar_properties %}
                        {% include 'partials/_property_card.html' with property=property %}
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}
{% endblock content %}
