<section class="py-16 lg:py-24 bg-surface-variant">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">{{ why_us_section.heading }}</h2>
            <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">{{ why_us_section.subheading }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for card in why_us_section_cards %}
                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary text-on-primary rounded-radius mx-auto mb-6 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fa-solid fa-{{ card.icon }} text-2xl"></i>
                    </div>
                    <h3 class="font-title text-xl font-semibold text-on-surface mb-4">{{ card.title }}</h3>
                    <p class="text-on-surface-variant">{{ card.description }}</p>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
