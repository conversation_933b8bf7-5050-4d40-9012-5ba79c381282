{% load static i18n %}

<section class="relative py-20 lg:py-32 bg-cover bg-center z-0">
    <div class="absolute inset-0 z-0">
        <img src="{{ hero_section.image.url }}"
             alt=""
             class="w-full h-full object-cover brightness-50">
    </div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
        <div class="text-center">
            <h1 class="mb-6 font-title text-4xl text-shadow-2xs text-gray-50 font-bold md:text-5xl lg:text-6xl">
                {{ hero_section.heading }}
            </h1>
            <p class="text-xl text-gray-50 text-shadow-sm mb-10 max-w-3xl mx-auto">{{ hero_section.subheading }}</p>

            <!-- Simple Search Bar -->
            <div class="max-w-4xl mx-auto">
                <form action="{% url 'core:property-list' %}"
                      method="get"
                      class="bg-surface rounded-radius shadow-lg p-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="location"
                               class="block text-sm text-left font-medium text-on-surface mb-1 ml-1">
                            {% trans "Локация" %}
                        </label>
                        <select id="location"
                                name="location"
                                class="w-full px-4 py-3 rounded-radius border border-outline bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="">{% trans "Избери локация" %}</option>
                            {% for location in locations %}<option value="{{ location.id }}">{{ location.name }}</option>{% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="property_type"
                               class="block text-sm text-left font-medium text-on-surface mb-1 ml-1">
                            {% trans "Тип имот" %}
                        </label>
                        <select id="property_type"
                                name="property_type"
                                class="w-full px-4 py-3 rounded-radius border border-outline bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="">{% trans "Всички типове" %}</option>
                            {% for property_type in property_types %}
                                <option value="{{ property_type.slug }}">{{ property_type.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="flex items-end mt-4 lg:mt-0">
                        <button type="submit" class="btn btn-primary w-full">
                            <svg class="w-5 h-5 inline mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z">
                                </path>
                            </svg>
                            {% trans "Търси имоти" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
