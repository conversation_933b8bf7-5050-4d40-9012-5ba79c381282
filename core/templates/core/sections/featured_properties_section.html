{% load i18n %}

<section class="py-16 lg:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">
                {{ featured_properties_section.heading }}
            </h2>
            <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">{{ featured_properties_section.subheading }}</p>
        </div>

        {% if featured_properties %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for property in featured_properties %}
                    {% include 'partials/_property_card.html' with property=property %}
                {% endfor %}
            </div>

            <div class="text-center mt-12">
                <a href="{% url 'core:property-list' %}" class="btn btn-lg btn-primary">{% trans "Виж всички имоти" %}</a>
            </div>
        {% else %}
            <div class="text-center py-12">
                <p class="text-on-surface-variant">{% trans "Все още няма препоръчани имоти." %}</p>
                <a href="{% url 'core:property-list' %}"
                   class="text-primary hover:underline">{% trans "Виж всички имоти" %}</a>
            </div>
        {% endif %}
    </div>
</section>
