{% load static i18n tailwind_tags %}

<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>
            {% block title %}
                {{ COMPANY.name }} - {% trans "Намерете имота на мечтите си" %}
            {% endblock title %}
        </title>
        <meta name="description"
              content="{% block description %}{% trans 'Професионални услуги за недвижими имоти в България. Апартаменти, къщи, офиси за продажба и под наем.' %}{% endblock description %}">
        <meta name="keywords"
              content="{% trans 'недвижими имоти, апартаменти, къщи, продажба, наем, България' %}">

        {# djlint:off #}
        <link rel="icon" href="{% static 'icons/favicon.ico' %}" type="image/x-icon" />
        <link rel="icon" type="image/png" sizes="16x16" href="{% static 'icons/icon-16.png' %}" />
        <link rel="icon" type="image/png" sizes="32x32" href="{% static 'icons/icon-32.png' %}" />
        <link rel="icon" type="image/png" sizes="48x48" href="{% static 'icons/icon-48.png' %}" />
        <link rel="icon" type="image/png" sizes="64x64" href="{% static 'icons/icon-64.png' %}" />
        {# djlint:on #}

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Merriweather:ital,opsz,wght@0,18..144,300..900;1,18..144,300..900&display=swap"
              rel="stylesheet">

        <!-- Font Awesome -->
        <link rel="stylesheet"
              href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

        <!-- Additional Scripts -->
        <script defer src="{% static 'js/components/themeManager.js' %}"></script>
        <script defer src="{% static 'js/components/languageSwitcher.js' %}"></script>
        <script defer src="{% static 'js/htmx.min.js' %}"></script>

        <!-- Alpine Plugins -->
        <script defer
                src="https://cdn.jsdelivr.net/npm/@alpinejs/focus@3.x.x/dist/cdn.min.js"></script>

        <!-- Alpine Core -->
        <script defer
                src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

        <!-- Tailwind CSS -->
        <script>
          // On page load or when changing themes, best to add inline in `head` to avoid FOUC
          (function() {
            const theme = localStorage.getItem('theme');
            if (theme === 'dark') {
              document.documentElement.setAttribute('data-theme', 'dark');
            } else {
              document.documentElement.setAttribute('data-theme', 'light');
            }
          })();
        </script>

        {% tailwind_css %}

        {% block extra_head %}
        {% endblock extra_head %}
    </head>

    <body class="bg-surface text-on-surface" x-data="themeManager">
        <!-- Header / Navigation -->
        {% include 'partials/_header.html' %}

        <!-- Main Content -->
        <main>
            {% block content %}
            {% endblock content %}
        </main>

        <!-- Footer -->
        {% include 'partials/_footer.html' %}

        {% block extra_scripts %}
        {% endblock extra_scripts %}

    </body>
</html>
