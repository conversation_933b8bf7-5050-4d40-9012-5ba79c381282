{% extends 'core/base.html' %}
{% load static i18n %}

{% block title %}
    {% trans "За нас" %} - {{ COMPANY.name }}
{% endblock title %}

{% block content %}
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-primary/10 to-secondary/5 py-20 lg:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="font-title text-4xl md:text-5xl lg:text-6xl font-bold text-on-surface mb-6">{{ site_about_us.heading }}</h1>
            <p class="text-xl text-on-surface-variant max-w-3xl mx-auto">{{ site_about_us.subheading }}</p>
        </div>
    </section>

    <!-- Story Section -->
    <section class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <div>
                    <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-6">{{ site_about_us.our_history_heading }}</h2>
                    <div class="prose max-w-none text-on-surface space-y-4">
                        <p class="text-lg">{{ site_about_us.our_history_content }}</p>
                    </div>
                </div>
                <div class="mt-12 lg:mt-0">
                    <div class="relative">
                        <div class="aspect-w-3 aspect-h-2 bg-surface-variant rounded-radius overflow-hidden">
                            <div class="flex items-center justify-center">
                                <svg class="w-24 h-24 text-on-surface-variant"
                                     fill="none"
                                     stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-16 lg:py-24 bg-surface-variant">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">{{ site_about_us.our_values_heading }}</h2>
                <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">{{ site_about_us.our_values_subheading }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for value in site_about_us.value_cards.all %}
                    {% include "partials/_our_values_card.html" with value=value %}
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Team Section -->
    {% if team_members %}
        <section class="py-16 lg:py-24">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">{{ site_about_us.our_team_heading }}</h2>
                    <p class="text-lg text-on-surface-variant max-w-2xl mx-auto">{{ site_about_us.our_team_subheading }}</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-24">
                    {% for member in team_members %}
                        {% include "partials/_broker_card.html" with member=member %}
                    {% endfor %}
                </div>
            </div>
        </section>
    {% endif %}

    <!-- CTA Section -->
    <section class="py-16 lg:py-24 bg-primary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-primary mb-6">{{ site_about_us.cta_heading }}</h2>
            <p class="text-xl text-on-primary/90 mb-8 max-w-2xl mx-auto">{{ site_about_us.cta_subheading }}</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'core:contact' %}"
                   class="inline-flex items-center justify-center bg-on-primary text-primary px-8 py-3 rounded-radius font-medium hover:bg-on-primary/90 transition-colors duration-200">
                    {% trans "Свържи се с нас" %}
                </a>
                <a href="{% url 'core:property-list' %}"
                   class="inline-flex items-center justify-center border-2 border-on-primary text-on-primary px-8 py-3 rounded-radius font-medium hover:bg-on-primary/10 transition-colors duration-200">
                    {% trans "Разгледай имотите" %}
                </a>
            </div>
        </div>
    </section>
{% endblock content %}
