{% extends 'core/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% trans "Имоти за продажба" %} - {{ COMPANY.name }}
{% endblock title %}

{% block content %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
         x-data="{ showFilters: false, totalCount: {{ total_count }} }"
             x-init="document.addEventListener('htmx:afterSettle', event => { if(event.target.id === 'property-results') { window.scrollTo({top: 0, behavior: 'smooth'}) } })">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">{% trans "Търсене на имоти" %}</h1>
            <p class="text-lg text-on-surface-variant">
                {% trans "Намерени са" %} <span class="font-semibold text-primary" x-text="totalCount"></span> {% trans "имота" %}
            </p>
        </div>

        <!-- Backdrop Overlay -->
        <div x-show="showFilters"
             x-transition:enter="transition-opacity ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="showFilters = false"
             class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"></div>

        <!-- Sliding Filters Sidebar -->
        <aside x-show="showFilters"
               x-transition:enter="transition ease-out duration-300"
               x-transition:enter-start="transform -translate-x-full"
               x-transition:enter-end="transform translate-x-0"
               x-transition:leave="transition ease-in duration-200"
               x-transition:leave-start="transform translate-x-0"
               x-transition:leave-end="transform -translate-x-full"
               class="fixed top-0 left-0 h-full w-full sm:w-96 bg-surface border-r border-outline shadow-2xl z-50 overflow-y-auto">

            <!-- Sidebar Header -->
            <div class="sticky top-0 bg-surface-variant border-b border-outline px-6 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="24"
                         height="24"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-sliders-vertical-icon lucide-sliders-vertical">
                        <line x1="4" x2="4" y1="21" y2="14" /><line x1="4" x2="4" y1="10" y2="3" /><line x1="12" x2="12" y1="21" y2="12" /><line x1="12" x2="12" y1="8" y2="3" /><line x1="20" x2="20" y1="21" y2="16" /><line x1="20" x2="20" y1="12" y2="3" /><line x1="2" x2="6" y1="14" y2="14" /><line x1="10" x2="14" y1="8" y2="8" /><line x1="18" x2="22" y1="16" y2="16" />
                    </svg>
                    <h2 class="font-title text-xl font-semibold text-on-surface ml-2">{% trans "Търсене и филтри" %}</h2>
                </div>
                <button @click="showFilters = false"
                        class="p-2 hover:bg-surface-variant rounded-radius transition-colors duration-200">
                    <svg class="w-6 h-6 text-on-surface"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Filters Form -->
            <div class="p-6">
                <form id="filter-form"
                      hx-get="{% url 'core:property-list' %}"
                      hx-target="#property-results"
                      hx-push-url="true"
                      hx-on::after-request="updateTotalCount(event)"
                      class="space-y-6">

                    <!-- Search Section -->
                    <div class="space-y-4">
                        <div class="flex items-center pb-2 border-b border-outline">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z">
                                </path>
                            </svg>
                            <h3 class="font-medium text-on-surface">{% trans "Търсене" %}</h3>
                        </div>
                        <div>
                            <label for="search" class="block text-sm font-medium text-on-surface mb-2">{% trans "Ключова дума" %}</label>
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="{{ current_filters.search|default:'' }}"
                                   placeholder="{% trans "Заглавие, описание, локация..." %}"
                                   class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                        </div>
                    </div>

                    <!-- Property Details Section -->
                    <div class="space-y-4">
                        <div class="flex items-center pb-2 border-b border-outline">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                            <h3 class="font-medium text-on-surface">{% trans "Детайли за имота" %}</h3>
                        </div>

                        <!-- Property Type -->
                        <div>
                            <label for="property_type"
                                   class="block text-sm font-medium text-on-surface mb-2">
                                {% trans "Тип имот" %}
                            </label>
                            <select id="property_type"
                                    name="property_type"
                                    class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">{% trans "Всички типове" %}</option>
                                {% for type in property_types %}
                                    <option value="{{ type.slug }}"
                                            {% if current_filters.property_type == type.slug %}selected{% endif %}>
                                        {{ type.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Location -->
                        <div>
                            <label for="location" class="block text-sm font-medium text-on-surface mb-2">{% trans "Локация" %}</label>
                            <select id="location"
                                    name="location"
                                    class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">{% trans "Всички локации" %}</option>
                                {% for loc in locations %}
                                    <option value="{{ loc.id }}"
                                            {% if current_filters.location == loc.id|stringformat:"s" %}selected{% endif %}>
                                        {{ loc.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Bedrooms -->
                        <div>
                            <label for="rooms" class="block text-sm font-medium text-on-surface mb-2">{% trans "Брой стаи" %}</label>
                            <select id="rooms"
                                    name="rooms"
                                    class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <option value="">{% trans "Всички" %}</option>
                                {% for i in "0123456789"|make_list %}
                                    <option value="{{ i }}"
                                            {% if current_filters.rooms == i %}selected{% endif %}>
                                        {% if i == "0" %}
                                            {% trans "Студио" %}
                                        {% else %}
                                            {{ i }}
                                            {% if i == "1" %}
                                                {% trans "спалня" %}
                                            {% else %}
                                                {% trans "спални" %}
                                            {% endif %}
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Price & Area Section -->
                    <div class="space-y-4">
                        <div class="flex items-center pb-2 border-b border-outline">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                            <h3 class="font-medium text-on-surface">{% trans "Цена и площ" %}</h3>
                        </div>

                        <!-- Price Range -->
                        <div>
                            <label class="block text-sm font-medium text-on-surface mb-3">{% trans "Ценов диапазон" %}</label>
                            <div class="grid grid-cols-2 gap-3">
                                <input type="number"
                                       name="min_price"
                                       value="{{ current_filters.min_price|default_if_none:'' }}"
                                       placeholder="{% trans "От" %}"
                                       class="px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <input type="number"
                                       name="max_price"
                                       value="{{ current_filters.max_price|default_if_none:'' }}"
                                       placeholder="{% trans "До" %}"
                                       class="px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                            </div>
                        </div>

                        <!-- Area Range -->
                        <div>
                            <label class="block text-sm font-medium text-on-surface mb-3">{% trans "Площ" %}</label>
                            <div class="grid grid-cols-2 gap-3">
                                <input type="number"
                                       name="min_area"
                                       value="{{ current_filters.min_area|default_if_none:'' }}"
                                       placeholder="{% trans "От" %}"
                                       class="px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                                <input type="number"
                                       name="max_area"
                                       value="{{ current_filters.max_area|default_if_none:'' }}"
                                       placeholder="{% trans "До" %}"
                                       class="px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200">
                            </div>
                        </div>
                    </div>

                    <!-- Features Section -->
                    <div class="space-y-4">
                        <div class="flex items-center pb-2 border-b border-outline">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                                </path>
                            </svg>
                            <h3 class="font-medium text-on-surface">{% trans "Екстри" %}</h3>
                        </div>
                        <div class="max-h-48 overflow-y-auto space-y-3 pr-2">
                            {% for feature in all_features %}
                                <label class="flex items-center group cursor-pointer">
                                    <input type="checkbox"
                                           name="features"
                                           value="{{ feature.id }}"
                                           {% if feature.id|stringformat:"s" in current_filters.features %}checked{% endif %}
                                           class="rounded border-outline text-primary focus:ring-primary focus:ring-offset-0 transition-all duration-200">
                                    <span class="ml-3 text-sm text-on-surface group-hover:text-primary transition-colors duration-200">{{ feature.name }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    </div>

                </form>
            </div>

            <!-- Action Buttons -->
            <div class="sticky bottom-0 left-0 grid grid-cols-2 gap-4 px-4 py-8 border-t border-outline bg-surface-variant">

                <div>
                    <a href="{% url 'core:property-list' %}"
                       @click="showFilters = false"
                       class="btn btn-primary btn-outline flex items-center justify-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg"
                             width="24"
                             height="24"
                             viewBox="0 0 24 24"
                             fill="none"
                             stroke="currentColor"
                             stroke-width="2"
                             stroke-linecap="round"
                             stroke-linejoin="round"
                             class="lucide lucide-trash2-icon lucide-trash-2">
                            <path d="M10 11v6" />
                            <path d="M14 11v6" />
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" />
                            <path d="M3 6h18" />
                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                        </svg>
                        {% trans "Изчисти" %}
                    </a>
                </div>

                <button form="filter-form"
                        type="submit"
                        @click="showFilters = false"
                        class="btn btn-primary flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="24"
                         height="24"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-search-icon lucide-search">
                        <path d="m21 21-4.34-4.34" />
                        <circle cx="11" cy="11" r="8" />
                    </svg>
                    {% trans "Търси" %}
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main>

            <!-- Filter Toggle Button -->
            <div class="flex justify-between mb-6 gap-4">
                <button @click="showFilters = true" class="flex items-center gap-2">
                    <i class="fa-solid fa-sliders"></i>
                    <span>{% trans "Филтри" %}</span>
                </button>

                <!-- Sort Options -->
                <div class="flex items-center">
                    <label for="sort_by" class="text-on-surface mr-2">{% trans "Подреди по:" %}</label>
                    <select id="sort_by"
                            name="sort_by"
                            hx-get="{% url 'core:property-list' %}"
                            hx-target="#property-results"
                            hx-include="[name]:not([name='sort_by'])"
                            hx-on::after-request="updateTotalCount(event)"
                            class="focus:outline-none focus:ring-none focus:border-none">
                        <option value="-created_at"
                                {% if current_filters.sort_by == "-created_at" %}selected{% endif %}>
                            {% trans "Най-нови" %}
                        </option>
                        <option value="created_at"
                                {% if current_filters.sort_by == "created_at" %}selected{% endif %}>
                            {% trans "Най-стари" %}
                        </option>
                        <option value="price"
                                {% if current_filters.sort_by == "price" %}selected{% endif %}>
                            {% trans "Цена ↑" %}
                        </option>
                        <option value="-price"
                                {% if current_filters.sort_by == "-price" %}selected{% endif %}>
                            {% trans "Цена ↓" %}
                        </option>
                        <option value="area"
                                {% if current_filters.sort_by == "area" %}selected{% endif %}>
                            {% trans "Площ ↑" %}
                        </option>
                        <option value="-area"
                                {% if current_filters.sort_by == "-area" %}selected{% endif %}>
                            {% trans "Площ ↓" %}
                        </option>
                    </select>
                </div>
            </div>

            <!-- Properties Grid -->
            <div id="property-results">{% include 'partials/property_list_results.html' %}</div>
        </main>
    </div>

    <script>
        function updateTotalCount(event) {
            if (event.detail.xhr.status === 200) {
                const headerCount = event.detail.xhr.getResponseHeader('X-Total-Count');
                const dataCount = event.detail.target.querySelector('[data-total-count]')?.getAttribute('data-total-count');
                const newCount = parseInt(headerCount || dataCount || '0');
                
                // Update the text content directly
                const countElement = document.querySelector('[x-text="totalCount"]');
                if (countElement) {
                    countElement.textContent = newCount;
                }
                
                // Also update Alpine data if possible
                const mainContainer = document.querySelector('[x-data]');
                if (mainContainer && mainContainer.__x) {
                    mainContainer.__x.$data.totalCount = newCount;
                }
            }
        }
    </script>
{% endblock content %}
