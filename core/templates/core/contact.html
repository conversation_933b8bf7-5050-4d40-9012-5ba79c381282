{% extends 'core/base.html' %}
{% load static i18n %}

{% block title %}
    {% trans "Контакти" %} - {{ COMPANY.name }}
{% endblock title %}

{% block content %}
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-primary/10 to-secondary/5 py-20 lg:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="font-title text-4xl md:text-5xl lg:text-6xl font-bold text-on-surface mb-6">{{ contact_page.heading }}</h1>
            <p class="text-xl text-on-surface-variant max-w-3xl mx-auto">{{ contact_page.subheading }}</p>
        </div>
    </section>

    <!-- Contact Info & Form Section -->
    <section class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-3 lg:gap-16">
                <!-- Contact Information -->
                <div class="lg:col-span-1">
                    <h2 class="font-title text-3xl font-bold text-on-surface mb-8">{{ contact_page.contact_heading }}</h2>

                    <!-- Office Address -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            {% trans "Адрес" %}
                        </h3>
                        <div class="text-on-surface-variant ml-7">
                            <p>{{ COMPANY.street }}</p>
                            <p>{{ COMPANY.city }}</p>
                        </div>
                    </div>

                    <!-- Phone Numbers -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                </path>
                            </svg>
                            {% trans "Телефон" %}
                        </h3>
                        <div class="text-on-surface-variant ml-7 space-y-2">
                            {% comment %} <div>
                                <a href="tel:+359888123456" class="hover:text-primary transition-colors duration-200">{{ COMPANY.phone }}</a>
                                <span class="text-sm">(Главен офис)</span>
                            </div> {% endcomment %}
                            <div>
                                <a href="tel:+359888123457"
                                   class="hover:text-primary transition-colors duration-200">{{ COMPANY.phone }}</a>
                            </div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                            {% trans "Имейл" %}
                        </h3>
                        <div class="text-on-surface-variant ml-7">
                            <a href="mailto:<EMAIL>"
                               class="hover:text-primary transition-colors duration-200">{{ COMPANY.email }}</a>
                        </div>
                    </div>

                    <!-- Working Hours -->
                    <div class="mb-8">
                        <h3 class="font-semibold text-on-surface mb-4 flex items-center">
                            <svg class="w-5 h-5 text-primary mr-2"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z">
                                </path>
                            </svg>
                            {% trans "Работно време" %}
                        </h3>
                        <div class="text-on-surface-variant ml-7 space-y-1">
                            <p class="leading-7">{{ COMPANY.working_hours|linebreaks }}</p>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div>
                        <h3 class="font-semibold text-on-surface mb-4">{% trans "Последвайте ни" %}</h3>
                        <div class="flex space-x-4">{% include 'partials/_social_media.html' %}</div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="lg:col-span-2 mt-12 lg:mt-0">
                    <div class="bg-surface border border-outline rounded-radius p-8">
                        <h2 class="font-title text-3xl font-bold text-on-surface mb-6">{{ contact_page.send_message_heading }}</h2>
                        <p class="text-on-surface-variant mb-8">{{ contact_page.send_message_subheading }}</p>

                        <form x-data="generalContactForm()"
                              @submit.prevent="submitForm()"
                              class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="contact_name"
                                           class="block text-sm font-medium text-on-surface mb-2">
                                        {% trans "Име *" %}
                                    </label>
                                    <input type="text"
                                           id="contact_name"
                                           name="name"
                                           x-model="form.name"
                                           required
                                           class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>

                                <div>
                                    <label for="contact_email"
                                           class="block text-sm font-medium text-on-surface mb-2">
                                        {% trans "Имейл *" %}
                                    </label>
                                    <input type="email"
                                           id="contact_email"
                                           name="email"
                                           x-model="form.email"
                                           required
                                           class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                            </div>

                            <div>
                                <label for="contact_phone"
                                       class="block text-sm font-medium text-on-surface mb-2">
                                    {% trans "Телефон" %}
                                </label>
                                <input type="tel"
                                       id="contact_phone"
                                       name="phone"
                                       x-model="form.phone"
                                       class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>

                            <div>
                                <label for="contact_subject"
                                       class="block text-sm font-medium text-on-surface mb-2">
                                    {% trans "Тема" %}
                                </label>
                                <select id="contact_subject"
                                        name="subject"
                                        x-model="form.subject"
                                        class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">{% trans "Изберете тема" %}</option>
                                    <option value="buying">{% trans "Купуване на имот" %}</option>
                                    <option value="selling">{% trans "Продажба на имот" %}</option>
                                    <option value="renting">{% trans "Наемане на имот" %}</option>
                                    <option value="valuation">{% trans "Оценка на имот" %}</option>
                                    <option value="consultation">{% trans "Консултация" %}</option>
                                    <option value="other">{% trans "Друго" %}</option>
                                </select>
                            </div>

                            <div>
                                <label for="contact_message"
                                       class="block text-sm font-medium text-on-surface mb-2">
                                    {% trans "Съобщение *" %}
                                </label>
                                <textarea id="contact_message"
                                          name="message"
                                          x-model="form.message"
                                          rows="6"
                                          required
                                          placeholder="{% trans 'Разкажете ни повече за вашите нужди...' %}"
                                          class="w-full px-4 py-3 border border-outline rounded-radius bg-surface text-on-surface focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox"
                                       id="contact_consent"
                                       name="consent"
                                       x-model="form.consent"
                                       required
                                       class="rounded border-outline text-primary focus:ring-primary">
                                <label for="contact_consent" class="ml-2 text-sm text-on-surface">
                                    {% trans "Съгласен/съгласна съм с обработката на личните ми данни съгласно" %}
                                    <a href="{% url 'core:privacy-policy' %}"
                                       class="text-primary hover:underline">{% trans "Политиката за поверителност" %}</a>
                                </label>
                            </div>

                            <button type="submit"
                                    :disabled="loading || !form.consent"
                                    :class="(loading || !form.consent) ? 'opacity-50 cursor-not-allowed' : ''"
                                    class="btn btn-primary w-full">
                                <span x-show="!loading">{% trans "Изпрати съобщение" %}</span>
                                <span x-show="loading" class="flex items-center justify-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-on-primary"
                                         fill="none"
                                         viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                        </path>
                                    </svg>
                                    {% trans "Изпраща..." %}
                                </span>
                            </button>

                            <!-- Success Message -->
                            <div x-show="success"
                                 x-transition
                                 class="p-4 bg-success/20 border border-success/30 rounded-radius">
                                <p class="text-success">{% trans "Съобщението е изпратено успешно! Ще се свържем с вас в най-кратко време." %}</p>
                            </div>

                            <!-- Error Message -->
                            <div x-show="error"
                                 x-transition
                                 class="p-4 bg-danger/20 border border-danger/30 rounded-radius">
                                <p class="text-danger" x-text="errorMessage"></p>
                            </div>
                        </form>

                        <script>
                    document.addEventListener('alpine:init', () => {
                        Alpine.data('generalContactForm', () => ({
                            form: {
                                name: '',
                                email: '',
                                phone: '',
                                subject: '',
                                message: '',
                                consent: false
                            },
                            loading: false,
                            success: false,
                            error: false,
                            errorMessage: '',

                            async submitForm() {
                                this.loading = true
                                this.success = false
                                this.error = false

                                try {
                                    const formData = new FormData()
                                    formData.append('name', this.form.name)
                                    formData.append('email', this.form.email)
                                    formData.append('phone', this.form.phone)
                                    
                                    let message = this.form.message
                                    if (this.form.subject) {
                                        message = `Тема: ${this.form.subject}\n\n${message}`
                                    }
                                    formData.append('message', message)

                                    const response = await fetch('{% url "core:contact-inquiry-create" %}', {
                                        method: 'POST',
                                        body: formData,
                                        headers: {
                                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                                        }
                                    })

                                    const data = await response.json()

                                    if (data.success) {
                                        this.success = true
                                        this.form = {
                                            name: '',
                                            email: '',
                                            phone: '',
                                            subject: '',
                                            message: '',
                                            consent: false
                                        }
                                    } else {
                                        this.error = true
                                        this.errorMessage = data.error || 'Възникна грешка при изпращане на съобщението.'
                                    }
                                } catch (err) {
                                    this.error = true
                                    this.errorMessage = 'Възникна грешка при изпращане на съобщението.'
                                } finally {
                                    this.loading = false
                                }
                            }
                        }))
                    })
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 lg:py-24 bg-surface-variant">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-title text-3xl lg:text-4xl font-bold text-on-surface mb-4">{{ contact_page.map_heading }}</h2>
                <p class="text-lg text-on-surface-variant">{{ contact_page.map_subheading }}</p>
            </div>

            <!-- Map placeholder -->
            <div class="w-full aspect-video bg-surface rounded-radius border border-outline flex items-center justify-center">
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2944.794273693235!2d25.6177454!3d42.432113699999995!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x40a86995f0b20623%3A0x858fa31e1a0df313!2sStara%20Zagora%20Centre%2C%20ul.%20%22Strandzha%22%208%2C%206004%20Stara%20Zagora!5e0!3m2!1sen!2sbg!4v1753637251084!5m2!1sen!2sbg"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"
                        class="w-full h-full"></iframe>
            </div>
        </div>
    </section>

    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}
{% endblock content %}
