{% load static i18n %}

<footer class="bg-surface-variant mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div class="lg:col-span-2">
                {% comment %} <div class="flex items-center mb-4">
                    <div class="p-2.5 bg-primary rounded-radius mr-3 flex items-center justify-center">
                        <i class="fa-solid fa-house text-on-primary text-lg"></i>
                    </div>
                    <span class="font-title text-xl font-bold text-on-surface">{{ COMPANY.name }}</span>
                </div> {% endcomment %}
                <div class="max-w-sm"><img src="{% static 'img/logo_full.png' %}" alt="logo"></div>
                <p class="text-on-surface-variant mb-4 mt-6 max-w-md">{{ COMPANY.description }}</p>
                <div class="flex space-x-4">
                    <!-- Social Media Links -->
                    {% include 'partials/_social_media.html' %}
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="font-title text-lg font-semibold text-on-surface mb-4">{% trans "Бързи линкове" %}</h3>
                <ul class="space-y-2">
                    <li>
                        <a href="{% url 'core:home' %}"
                           class="text-on-surface-variant hover:text-primary transition-colors duration-200">{% trans "Начало" %}</a>
                    </li>
                    <li>
                        <a href="{% url 'core:property-list' %}"
                           class="text-on-surface-variant hover:text-primary transition-colors duration-200">
                            {% trans "Виж всички имоти" %}
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'core:about' %}"
                           class="text-on-surface-variant hover:text-primary transition-colors duration-200">{% trans "За нас" %}</a>
                    </li>
                    <li>
                        <a href="{% url 'core:contact' %}"
                           class="text-on-surface-variant hover:text-primary transition-colors duration-200">
                            {% trans "Контакти" %}
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="font-title text-lg font-semibold text-on-surface mb-4">{% trans "Контакти" %}</h3>
                <ul class="space-y-2">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-on-surface-variant mr-2"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                            </path>
                        </svg>
                        <span class="text-on-surface-variant">{{ COMPANY.phone }}</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-on-surface-variant mr-2"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                            </path>
                        </svg>
                        <span class="text-on-surface-variant">{{ COMPANY.email }}</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-on-surface-variant mr-2 mt-0.5"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-on-surface-variant">{{ COMPANY.street }}
                            <br>
                        {{ COMPANY.city }}</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="mt-12 pt-8 border-t border-outline">
            <div class="flex flex-col gap-4 lg:flex-row lg:justify-between lg:items-center">
                <p class="order-2 lg:order-1 group text-on-surface-variant text-sm">
                    {% trans "Уебсайтът е разработен и поддържан от" %}
                    <a href="https://saitove-beroe.com"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="group-hover:text-orange-700 transition-colors duration-200">{% trans "Сайтове Берое" %}</a>.
                </p>
                <div class="order-1 lg:order-2 flex space-x-6 mt-4 lg:mt-0">
                    <a href="{% url 'core:privacy-policy' %}"
                       class="text-on-surface-variant hover:text-primary text-sm transition-colors duration-200">
                        {% trans "Политика за поверителност" %}
                    </a>
                    <a href="{% url 'core:terms-of-service' %}"
                       class="text-on-surface-variant hover:text-primary text-sm transition-colors duration-200">
                        {% trans "Общи условия" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>
