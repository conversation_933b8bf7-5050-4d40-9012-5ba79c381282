{% load i18n %}

{% comment %} Desktop Pagination {% endcomment %}
<nav class="flex items-center space-x-2">
    {% comment %} First Button {% endcomment %}
    {% if page_obj.has_previous %}
        <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                hx-get="{% url 'core:property-list' %}?page=1{{ filter_query_string }}"
                hx-target="#property-results"
                hx-push-url="true"
                hx-swap="innerHTML"
                hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
            <i class="fa-solid fa-angles-left"></i>
        </button>
    {% else %}
        <button class="btn btn-outline btn-secondary size-10 opacity-50 cursor-not-allowed flex items-center justify-center"
                disabled>
            <i class="fa-solid fa-angles-left"></i>
        </button>
    {% endif %}

    {% comment %} Previous Button {% endcomment %}
    {% if page_obj.has_previous %}
        <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                hx-get="{% url 'core:property-list' %}?page={{ page_obj.previous_page_number }}{{ filter_query_string }}"
                hx-target="#property-results"
                hx-push-url="true"
                hx-swap="innerHTML"
                hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
            <i class="fa-solid fa-angle-left"></i>
        </button>
    {% else %}
        <button class="btn btn-outline btn-secondary size-10 opacity-50 cursor-not-allowed flex items-center justify-center"
                disabled>
            <i class="fa-solid fa-angle-left"></i>
        </button>
    {% endif %}

    <!-- Page Numbers -->
    {% comment %}
        Show maximum 5 page numbers with smart range calculation
    {% endcomment %}

    {% for num in page_obj.paginator.page_range %}
        {% comment %} Calculate if this page should be shown {% endcomment %}
        {% with show_page=False %}
            {% comment %} Always show if total pages <= 5 {% endcomment %}
            {% if page_obj.paginator.num_pages <= 5 %}
                {% with show_page=True %}
                    {% if page_obj.number == num %}
                        <button class="btn btn-primary size-10 flex items-center justify-center">{{ num }}</button>
                    {% else %}
                        <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                                hx-get="{% url 'core:property-list' %}?page={{ num }}{{ filter_query_string }}"
                                hx-target="#property-results"
                                hx-push-url="true"
                                hx-swap="innerHTML"
                                hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
                            {{ num }}
                        </button>
                    {% endif %}
                {% endwith %}
            {% else %}
                {% comment %} For more than 5 pages, show 5 pages around current {% endcomment %}
                {% if page_obj.number <= 3 %}
                    {% comment %} Show pages 1-5 {% endcomment %}
                    {% if num <= 5 %}
                        {% if page_obj.number == num %}
                            <button class="btn btn-primary size-10 flex items-center justify-center">{{ num }}</button>
                        {% else %}
                            <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                                    hx-get="{% url 'core:property-list' %}?page={{ num }}{{ filter_query_string }}"
                                    hx-target="#property-results"
                                    hx-push-url="true"
                                    hx-swap="innerHTML"
                                    hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
                                {{ num }}
                            </button>
                        {% endif %}
                    {% endif %}
                {% elif page_obj.number >= page_obj.paginator.num_pages|add:"-2" %}
                    {% comment %} Show last 5 pages {% endcomment %}
                    {% if num >= page_obj.paginator.num_pages|add:"-4" %}
                        {% if page_obj.number == num %}
                            <button class="btn btn-primary size-10 flex items-center justify-center">{{ num }}</button>
                        {% else %}
                            <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                                    hx-get="{% url 'core:property-list' %}?page={{ num }}{{ filter_query_string }}"
                                    hx-target="#property-results"
                                    hx-push-url="true"
                                    hx-swap="innerHTML"
                                    hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
                                {{ num }}
                            </button>
                        {% endif %}
                    {% endif %}
                {% else %}
                    {% comment %} Show current page ±2 (5 pages centered) {% endcomment %}
                    {% if num >= page_obj.number|add:"-2" and num <= page_obj.number|add:"2" %}
                        {% if page_obj.number == num %}
                            <button class="btn btn-primary size-10 flex items-center justify-center">{{ num }}</button>
                        {% else %}
                            <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                                    hx-get="{% url 'core:property-list' %}?page={{ num }}{{ filter_query_string }}"
                                    hx-target="#property-results"
                                    hx-push-url="true"
                                    hx-swap="innerHTML"
                                    hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
                                {{ num }}
                            </button>
                        {% endif %}
                    {% endif %}
                {% endif %}
            {% endif %}
        {% endwith %}
    {% endfor %}

    <!-- Next Button -->
    {% if page_obj.has_next %}
        <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                hx-get="{% url 'core:property-list' %}?page={{ page_obj.next_page_number }}{{ filter_query_string }}"
                hx-target="#property-results"
                hx-push-url="true"
                hx-swap="innerHTML"
                hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
            <i class="fa-solid fa-angle-right"></i>
        </button>
    {% else %}
        <button class="btn btn-outline btn-secondary opacity-50 cursor-not-allowed size-10 flex items-center justify-center"
                disabled>
            <i class="fa-solid fa-angle-right"></i>
        </button>
    {% endif %}

    {% comment %} Last Button {% endcomment %}
    {% if page_obj.has_next %}
        <button class="btn btn-outline btn-secondary size-10 flex items-center justify-center"
                hx-get="{% url 'core:property-list' %}?page={{ page_obj.paginator.num_pages }}{{ filter_query_string }}"
                hx-target="#property-results"
                hx-push-url="true"
                hx-swap="innerHTML"
                hx-on:htmx:afterSettle="window.scrollTo({top: 0, behavior: 'smooth'})">
            <i class="fa-solid fa-angles-right"></i>
        </button>
    {% else %}
        <button class="btn btn-outline btn-secondary size-10 opacity-50 cursor-not-allowed flex items-center justify-center"
                disabled>
            <i class="fa-solid fa-angles-right"></i>
        </button>
    {% endif %}
</nav>

<!-- Page Info -->
{% comment %}
<div class="mt-4 text-center text-sm text-on-surface-variant">
    {% trans "Страница" %} {{ page_obj.number }} {% trans "от" %} {{ page_obj.paginator.num_pages }}
</div>
{% endcomment %}
