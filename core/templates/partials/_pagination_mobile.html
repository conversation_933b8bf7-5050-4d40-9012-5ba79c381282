{% load i18n %}

<nav class="w-full max-w-80 flex items-center justify-center space-x-3">
    <div class="flex items-center space-x-2 overflow-x-auto scrollbar-hide scroll-smooth"
         x-data
         x-init=" if ({{ page_obj.number }} > 3) { const container = $el; const activeLink = $refs.activePage; if (activeLink) { const containerRect = container.getBoundingClientRect(); const linkRect = activeLink.getBoundingClientRect(); const scrollLeft = linkRect.left - containerRect.left - (containerRect.width / 2) + (linkRect.width / 2); container.scrollLeft = scrollLeft; } }">
        {% for num in page_obj.paginator.page_range %}
            <div>
                {% if page_obj.number == num %}
                    <button x-ref="activePage"
                            class="btn btn-primary size-16 text-lg flex items-center justify-center">{{ num }}</button>
                {% else %}
                    <button class="btn btn-outline btn-secondary size-16 text-lg flex flex-1 items-center justify-center"
                            hx-get="{% url 'core:property-list' %}?page={{ num }}{{ filter_query_string }}"
                            hx-target="#property-results"
                            hx-swap="innerHTML"
                            hx-push-url="true"
                            hx-indicator="#loading-indicator">{{ num }}</button>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</nav>
