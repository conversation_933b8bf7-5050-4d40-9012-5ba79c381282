<div x-data="{ videoModalIsOpen: false }"
     x-effect="document.getElementById('header').classList.toggle('hidden', videoModalIsOpen); ">
    <button x-on:click="videoModalIsOpen = true"
            type="button"
            class="relative size-16 flex-shrink-0 rounded-radius overflow-hidden">
        <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 size-8 bg-slate-200 rounded-full p-2 flex items-center justify-center">
            <i class="fa-solid fa-play text-red-600 z-10"></i>
        </div>
        <img src="{{ video.thumbnail_url }}"
             alt="{{ video.title }}"
             class="size-full object-cover brightness-50">
    </button>

    <div x-cloak
         x-show="videoModalIsOpen"
         x-transition.opacity.duration.200ms
         x-trap.inert.noscroll="videoModalIsOpen"
         x-on:keydown.esc.window="videoModalIsOpen = false"
         x-on:click.self="videoModalIsOpen = false; $refs.youtubeIframe.src = $refs.youtubeIframe.src"
         class="fixed inset-0 z-30 flex items-center justify-center bg-black/20 p-4 backdrop-blur-md lg:p-8"
         role="dialog"
         aria-modal="true"
         aria-labelledby="videoModalTitle">
        <!-- Modal Dialog -->
        <div x-show="videoModalIsOpen"
             x-transition:enter="transition ease-out duration-300 delay-200"
             x-transition:enter-start="opacity-0 translate-y-8"
             x-transition:enter-end="opacity-100 translate-y-0"
             class="max-w-2xl w-full relative">
            <!-- Close Button -->
            <button type="button"
                    x-show="videoModalIsOpen"
                    x-on:click="videoModalIsOpen = false; $refs.youtubeIframe.src = $refs.youtubeIframe.src"
                    x-transition:enter="transition ease-out duration-200 delay-500"
                    x-transition:enter-start="opacity-0 scale-0"
                    x-transition:enter-end="opacity-100 scale-100"
                    class="absolute -top-12 right-0 flex items-center justify-center rounded-full bg-slate-200 p-1.5 text-on-surface-strong hover:opacity-75 active:opacity-100 backdrop-blur-sm dark:bg-surface-dark-alt dark:text-on-surface-dark-strong"
                    aria-label="close modal">
                <svg xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 24 24"
                     aria-hidden="true"
                     stroke="currentColor"
                     fill="none"
                     stroke-width="1.4"
                     class="w-4 h-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            <!-- YouTube Video -->
            <div class="aspect-video w-full rounded-radius overflow-hidden">
                <iframe x-ref="youtubeIframe"
                        class="w-full h-full"
                        src="{{ video.embed_url }}"
                        title="YouTube video"
                        frameborder="0"
                        allow="autoplay; encrypted-media"
                        allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>
