<div x-cloak
     x-show="imageModalIsOpen"
     x-transition.opacity.duration.200ms
     x-trap.inert.noscroll="imageModalIsOpen"
     x-on:keydown.esc.window="imageModalIsOpen = false"
     x-on:click.self="imageModalIsOpen = false; $refs.youtubeIframe.src = $refs.youtubeIframe.src"
     class="fixed inset-0 z-30 flex items-center justify-center bg-black/20 p-4 backdrop-blur-md lg:p-8"
     role="dialog"
     aria-modal="true"
     aria-labelledby="imageModalTitle">
    <!-- Modal Dialog -->
    <div x-show="imageModalIsOpen"
         x-transition:enter="transition ease-out duration-300 delay-200"
         x-transition:enter-start="opacity-0 translate-y-8"
         x-transition:enter-end="opacity-100 translate-y-0"
         class="max-w-4xl lg:max-w-7xl w-full relative">
        <!-- Close Button -->
        <button type="button"
                x-show="imageModalIsOpen"
                x-on:click="imageModalIsOpen = false; $refs.youtubeIframe.src = $refs.youtubeIframe.src"
                x-transition:enter="transition ease-out duration-200 delay-500"
                x-transition:enter-start="opacity-0 scale-0"
                x-transition:enter-end="opacity-100 scale-100"
                class="absolute -top-12 right-0 flex items-center justify-center rounded-full bg-slate-200 p-1.5 text-on-surface-strong hover:opacity-75 active:opacity-100 backdrop-blur-sm dark:bg-surface-dark-alt dark:text-on-surface-dark-strong"
                aria-label="close modal">
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 aria-hidden="true"
                 stroke="currentColor"
                 fill="none"
                 stroke-width="1.4"
                 class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>

        <!-- Image -->
        <div>

            <!-- previous button -->
            <button type="button"
                    class="absolute backdrop-blur-sm left-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-surface/40 p-2 text-on-surface transition hover:bg-surface/60 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:outline-offset-0 dark:bg-surface-dark/40 dark:text-on-surface-dark dark:hover:bg-surface-dark/60 dark:focus-visible:outline-primary-dark"
                    aria-label="previous slide"
                    x-on:click="previous()">
                <svg xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 24 24"
                     stroke="currentColor"
                     fill="none"
                     stroke-width="3"
                     class="size-5 md:size-6 pr-0.5"
                     aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
                </svg>
            </button>

            <!-- next button -->
            <button type="button"
                    class="absolute backdrop-blur-sm right-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-surface/40 p-2 text-on-surface transition hover:bg-surface/60 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:outline-offset-0 dark:bg-surface-dark/40 dark:text-on-surface-dark dark:hover:bg-surface-dark/60 dark:focus-visible:outline-primary-dark"
                    aria-label="next slide"
                    x-on:click="next()">
                <svg xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 24 24"
                     stroke="currentColor"
                     fill="none"
                     stroke-width="3"
                     class="size-5 md:size-6 pl-0.5"
                     aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                </svg>
            </button>

            <!-- slides -->
            <div class="relative aspect-[3/2] w-full rounded-radius overflow-hidden"
                 x-on:touchstart="handleTouchStart($event)"
                 x-on:touchmove="handleTouchMove($event)"
                 x-on:touchend="handleTouchEnd()"
                 @click="imageModalIsOpen = true">
                <template x-for="(slide, index) in slides">
                    <div x-show="currentSlideIndex == index + 1"
                         class="absolute inset-0"
                         x-transition.opacity.duration.700ms>
                        <img class="absolute w-full h-full inset-0 object-cover text-on-surface dark:text-on-surface-dark"
                             x-bind:src="slide.imgSrc"
                             x-bind:alt="slide.imgAlt" />
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>
