<div class="text-center group">
    <div class="relative mb-6">
        {% if member.photo %}
            <img src="{{ member.photo.url }}"
                 alt="{{ member.name }}"
                 class="w-32 h-32 rounded-full object-cover mx-auto group-hover:scale-105 transition-transform duration-200">
        {% else %}
            <div class="w-32 h-32 rounded-full flex items-center justify-center mx-auto group-hover:scale-105 transition-transform duration-200 overflow-hidden border border-outline bg-surface-alt text-on-surface/50 dark:border-outline-dark dark:bg-surface-dark-alt dark:text-on-surface-dark/50">
                <svg xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 24 24"
                     aria-hidden="true"
                     fill="currentColor"
                     class="w-full h-full mt-3">
                    <path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" clip-rule="evenodd" />
                </svg>
            </div>
        {% endif %}
    </div>
    <h3 class="font-title text-xl font-semibold text-on-surface mb-2">{{ member.name }}</h3>
    <p class="text-on-surface-variant mb-4">{{ member.title }}</p>
    <div class="space-y-2">
        <a href="tel:{{ member.phone }}"
           class="flex items-center justify-center text-primary hover:text-primary/80 transition-colors duration-200">
            <svg class="w-4 h-4 mr-2"
                 fill="none"
                 stroke="currentColor"
                 viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                </path>
            </svg>
            {{ member.phone }}
        </a>
        <a href="mailto:{{ member.email }}"
           class="flex items-center justify-center text-primary hover:text-primary/80 transition-colors duration-200">
            <svg class="w-4 h-4 mr-2"
                 fill="none"
                 stroke="currentColor"
                 viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                </path>
            </svg>
            {{ member.email }}
        </a>
    </div>
</div>
