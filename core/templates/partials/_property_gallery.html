<script>
    function propertyGallery() {
        return {
            mediaItems: [],
            mediaLoaded: false,
            currentMediaIndex: 1,
            modalOpen: false,
            modalMediaIndex: 1,
            touchStartX: null,
            touchEndX: null,
            swipeThreshold: 50,
            
            init() {
                // Load media items from server
                this.mediaItems = window.propertyMediaItems || [];
                
                // Set first item for preview if exists
                if (this.mediaItems.length > 0) {
                    this.mediaLoaded = true;
                }
            },
            
            // Gallery navigation
            previous() {
                if (this.currentMediaIndex > 1) {
                    this.currentMediaIndex = this.currentMediaIndex - 1
                } else {
                    this.currentMediaIndex = this.mediaItems.length
                }
            },
            
            next() {
                if (this.currentMediaIndex < this.mediaItems.length) {
                    this.currentMediaIndex = this.currentMediaIndex + 1
                } else {
                    this.currentMediaIndex = 1
                }
            },
            
            handleTouchStart(event) {
                this.touchStartX = event.touches[0].clientX
            },
            
            handleTouchMove(event) {
                this.touchEndX = event.touches[0].clientX
            },
            
            handleTouchEnd() {
                if(this.touchEndX){
                    if (this.touchStartX - this.touchEndX > this.swipeThreshold) {
                        this.next()
                    }
                    if (this.touchStartX - this.touchEndX < -this.swipeThreshold) {
                        this.previous()
                    }
                    this.touchStartX = null
                    this.touchEndX = null
                }
            },
            
            // Modal functionality
            openModal(mediaIndex) {
                this.modalMediaIndex = mediaIndex;
                this.modalOpen = true;
                document.body.style.overflow = 'hidden';
            },
            
            closeModal() {
                this.modalOpen = false;
                document.body.style.overflow = '';
                // Stop any playing videos
                const iframes = document.querySelectorAll('.modal-video-iframe');
                iframes.forEach(iframe => {
                    iframe.src = iframe.src;
                });
            },
            
            previousModal() {
                if (this.modalMediaIndex > 1) {
                    this.modalMediaIndex = this.modalMediaIndex - 1
                } else {
                    this.modalMediaIndex = this.mediaItems.length
                }
            },
            
            nextModal() {
                if (this.modalMediaIndex < this.mediaItems.length) {
                    this.modalMediaIndex = this.modalMediaIndex + 1
                } else {
                    this.modalMediaIndex = 1
                }
            },
            
            handleModalTouchStart(event) {
                this.touchStartX = event.touches[0].clientX
            },
            
            handleModalTouchMove(event) {
                this.touchEndX = event.touches[0].clientX
            },
            
            handleModalTouchEnd() {
                if(this.touchEndX){
                    if (this.touchStartX - this.touchEndX > this.swipeThreshold) {
                        this.nextModal()
                    }
                    if (this.touchStartX - this.touchEndX < -this.swipeThreshold) {
                        this.previousModal()
                    }
                    this.touchStartX = null
                    this.touchEndX = null
                }
            },
            
            getCurrentMedia() {
                return this.mediaItems[this.currentMediaIndex - 1] || {};
            },
            
            getModalMedia() {
                return this.mediaItems[this.modalMediaIndex - 1] || {};
            }
        }
    }
</script>

<script>
    // Initialize media items from Django context
    window.propertyMediaItems = {{ media_items_json|safe }};
</script>

<div x-data="propertyGallery()"
     x-init="init()"
     class="w-full overflow-hidden">

    <div class="relative">

        <!-- previous button -->
        <button type="button"
                x-show="mediaItems.length > 1"
                class="absolute backdrop-blur-sm left-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-surface/40 p-2 text-on-surface transition hover:bg-surface/60 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:outline-offset-0 dark:bg-surface-dark/40 dark:text-on-surface-dark dark:hover:bg-surface-dark/60 dark:focus-visible:outline-primary-dark"
                aria-label="previous media"
                x-on:click="previous()">
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 fill="none"
                 stroke-width="3"
                 class="size-5 md:size-6 pr-0.5"
                 aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
            </svg>
        </button>

        <!-- next button -->
        <button type="button"
                x-show="mediaItems.length > 1"
                class="absolute backdrop-blur-sm right-5 top-1/2 z-20 flex rounded-full -translate-y-1/2 items-center justify-center bg-surface/40 p-2 text-on-surface transition hover:bg-surface/60 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:outline-offset-0 dark:bg-surface-dark/40 dark:text-on-surface-dark dark:hover:bg-surface-dark/60 dark:focus-visible:outline-primary-dark"
                aria-label="next media"
                x-on:click="next()">
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 fill="none"
                 stroke-width="3"
                 class="size-5 md:size-6 pl-0.5"
                 aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
        </button>

        <!-- media display -->
        <div class="relative min-h-[50svh] w-full rounded-radius overflow-hidden"
             x-on:touchstart="handleTouchStart($event)"
             x-on:touchmove="handleTouchMove($event)"
             x-on:touchend="handleTouchEnd()">
            <template x-for="(media, index) in mediaItems">
                <div x-show="currentMediaIndex == index + 1"
                     class="absolute group inset-0"
                     x-transition.opacity.duration.700ms>

                    <!-- Image -->
                    <template x-if="media.type === 'image'">
                        <img class="absolute w-full h-full inset-0 object-cover text-on-surface dark:text-on-surface-dark cursor-pointer"
                             x-bind:src="media.url"
                             x-bind:alt="media.alt_text"
                             x-on:click="openModal(index + 1)" />
                    </template>

                    <!-- Video Thumbnail -->
                    <template x-if="media.type === 'video'">
                        <div class="absolute inset-0 cursor-pointer"
                             x-on:click="openModal(index + 1)">
                            <img class="absolute w-full h-full inset-0 object-cover brightness-75"
                                 x-bind:src="media.thumbnail_url"
                                 x-bind:alt="media.title" />
                            <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 size-16 bg-black/50 group-hover:bg-red-600 rounded-full p-2 flex items-center justify-center backdrop-blur-sm transition-colors duration-200">
                                <svg class="size-full text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                            </div>
                        </div>
                    </template>
                </div>
            </template>
        </div>
    </div>

    {% comment %} Thumbnails {% endcomment %}
    <div class="flex gap-2 pt-2 max-w-full overflow-x-auto">
        <template x-for="(media, index) in mediaItems">
            <div class="relative size-16 flex-shrink-0 rounded-radius overflow-hidden cursor-pointer"
                 x-on:click="currentMediaIndex = index + 1"
                 :class="[currentMediaIndex === index + 1 ? 'border-2 border-primary' : '']">

                <!-- Image Thumbnail -->
                <template x-if="media.type === 'image'">
                    <img x-bind:src="media.thumbnail_url"
                         x-bind:alt="media.alt_text"
                         class="size-full object-cover">
                </template>

                <!-- Video Thumbnail -->
                <template x-if="media.type === 'video'">
                    <div class="relative size-full">
                        <img x-bind:src="media.thumbnail_url"
                             x-bind:alt="media.title"
                             class="size-full object-cover brightness-75">
                        <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 size-6 bg-red-600 rounded-full flex items-center justify-center">
                            <svg class="size-full text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z" />
                            </svg>
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </div>

    <!-- Unified Media Modal -->
    <div x-cloak
         x-show="modalOpen"
         x-transition.opacity.duration.300
         x-trap.inert.noscroll="modalOpen"
         x-on:keydown.esc.window="closeModal()"
         x-on:keydown.arrow-left.window="previousModal()"
         x-on:keydown.arrow-right.window="nextModal()"
         class="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm"
         role="dialog"
         aria-modal="true"
         aria-labelledby="modalMediaTitle">

        <!-- Close Button -->
        <button type="button"
                x-on:click="closeModal()"
                class="absolute top-4 right-4 z-60 flex items-center justify-center rounded-full bg-black/50 p-3 text-white hover:bg-black/70 transition-colors backdrop-blur-sm"
                aria-label="Close modal">
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 fill="none"
                 stroke-width="2"
                 class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>

        <!-- Previous Button -->
        <button type="button"
                x-show="mediaItems.length > 1"
                x-on:click="previousModal()"
                class="absolute left-4 top-1/2 z-60 flex items-center justify-center rounded-full bg-black/50 p-3 text-white hover:bg-black/70 transition-colors backdrop-blur-sm -translate-y-1/2"
                aria-label="Previous media">
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 fill="none"
                 stroke-width="2"
                 class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
            </svg>
        </button>

        <!-- Next Button -->
        <button type="button"
                x-show="mediaItems.length > 1"
                x-on:click="nextModal()"
                class="absolute right-4 top-1/2 z-60 flex items-center justify-center rounded-full bg-black/50 p-3 text-white hover:bg-black/70 transition-colors backdrop-blur-sm -translate-y-1/2"
                aria-label="Next media">
            <svg xmlns="http://www.w3.org/2000/svg"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 fill="none"
                 stroke-width="2"
                 class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
        </button>

        <!-- Media Container -->
        <div class="relative w-full h-full flex items-center justify-center p-4"
             x-on:touchstart="handleModalTouchStart($event)"
             x-on:touchmove="handleModalTouchMove($event)"
             x-on:touchend="handleModalTouchEnd()"
             x-on:click.self="closeModal()">

            <template x-for="(media, index) in mediaItems">
                <div x-show="modalMediaIndex == index + 1"
                     class="absolute inset-4 flex items-center justify-center"
                     x-transition.opacity.duration.300>

                    <!-- Image Display -->
                    <template x-if="media.type === 'image'">
                        <img class="max-w-full max-h-full object-contain"
                             @click.outside="closeModal()"
                             x-bind:src="media.url"
                             x-bind:alt="media.alt_text"
                             id="modalMediaTitle" />
                    </template>

                    <!-- Video Display -->
                    <template x-if="media.type === 'video'">
                        <div class="w-[90vw] h-[50.625vw] max-w-[1200px] max-h-[675px] md:w-[80vw] md:h-[45vw] lg:w-[70vw] lg:h-[39.375vw]">
                            <iframe class="modal-video-iframe w-full h-full rounded-lg"
                                    @click.outside="closeModal()"
                                    x-bind:src="media.embed_url"
                                    x-bind:title="media.title"
                                    frameborder="0"
                                    allow="autoplay; encrypted-media; picture-in-picture"
                                    allowfullscreen></iframe>
                        </div>
                    </template>
                </div>
            </template>

            <!-- Media Counter -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                <span x-text="modalMediaIndex"></span> / <span x-text="mediaItems.length"></span>
            </div>
        </div>
    </div>

</div>
