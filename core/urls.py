from django.urls import path
from . import views
# Import property views for backward compatibility with existing templates
from properties import views as property_views

app_name = 'core'

urlpatterns = [
    # Property routes (for legacy template references)
    path('properties/', property_views.property_list_view, name='property-list'),
    path('properties/<slug:slug>/', property_views.property_detail_view, name='property-detail'),
    # Начална страница
    path('', views.home_view, name='home'),
    

    # Обработка на формите за контакт
    path('contact-inquiry/', views.contact_inquiry_create_view, name='contact-inquiry-create'),
    
    # Статични страници
    path('about/', views.AboutView.as_view(), name='about'),
    path('contact/', views.ContactView.as_view(), name='contact'),
    path('privacy-policy/', views.PrivacyPolicyView.as_view(), name='privacy-policy'),
    path('terms-of-service/', views.TermsOfServiceView.as_view(), name='terms-of-service'),
]
