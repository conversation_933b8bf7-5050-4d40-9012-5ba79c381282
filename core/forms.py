"""
This file is intentionally left empty.

All forms related to properties have been moved to the 'properties' app.
"""


def validate_image_dimensions(
    image, min_width=300, min_height=200, max_width=5000, max_height=5000
):
    """
    Валидира размерите на изображението

    Args:
        image: PIL Image обект или Django UploadedFile
        min_width: Минимална ширина в пиксели
        min_height: Мини<PERSON><PERSON>л<PERSON> височина в пиксели
        max_width: Максимална ширина в пиксели
        max_height: Максимална височина в пиксели

    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        from PIL import Image

        if hasattr(image, "read"):
            img = Image.open(image)
        else:
            img = image

        width, height = img.size

        if width < min_width or height < min_height:
            return (
                False,
                f"Изображението е твърде малко. Минималните размери са {min_width}x{min_height} пиксела.",
            )

        if width > max_width or height > max_height:
            return (
                False,
                f"Изображението е твърде голямо. Максималните размери са {max_width}x{max_height} пиксела.",
            )

        return True, ""

    except Exception as e:
        return False, f"Грешка при проверка на размерите: {str(e)}"


def sanitize_filename(filename):
    """
    Почиства името на файла от потенциално опасни символи

    Args:
        filename: Оригиналното име на файла

    Returns:
        str: Почистеното име на файла
    """
    import re
    import os

    # Извличаме името и разширението
    name, ext = os.path.splitext(filename)

    # Премахваме всички символи освен букви, цифри, тире и долни черти
    name = re.sub(r"[^\w\-_\.]", "_", name)

    # Ограничаваме дължината на името
    if len(name) > 50:
        name = name[:50]

    # Връщаме почистеното име с разширението
    return f"{name}{ext.lower()}"
