# Generated by Django 5.2.4 on 2025-07-28 15:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0016_privacypolicy_created_at_privacypolicy_updated_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='TermsOfService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(verbose_name='Текст')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на създаване')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Дата на последно обновяване')),
            ],
            options={
                'verbose_name': 'Общи условия',
                'verbose_name_plural': 'Общи условия',
            },
        ),
    ]
