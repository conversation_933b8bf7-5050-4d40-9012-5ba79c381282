# Generated by Django 5.2.4 on 2025-07-28 13:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0014_alter_valuecard_options_valuecard_order'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrivacyPolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(verbose_name='Текст')),
                ('text_bg', models.TextField(null=True, verbose_name='Текст')),
                ('text_en', models.TextField(null=True, verbose_name='Текст')),
                ('text_el', models.TextField(null=True, verbose_name='Текст')),
                ('text_ru', models.TextField(null=True, verbose_name='Текст')),
            ],
            options={
                'verbose_name': 'Политика за поверителност',
                'verbose_name_plural': 'Политика за поверителност',
            },
        ),
    ]
