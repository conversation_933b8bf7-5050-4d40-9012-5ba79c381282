# Generated by Django 5.2.4 on 2025-07-28 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_alter_contactpage_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteAboutUs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('heading', models.CharField(max_length=255, verbose_name='Заглавие')),
                ('heading_bg', models.CharField(max_length=255, null=True, verbose_name='Заглавие')),
                ('heading_en', models.Char<PERSON>ield(max_length=255, null=True, verbose_name='Заглавие')),
                ('heading_el', models.Char<PERSON>ield(max_length=255, null=True, verbose_name='Заглавие')),
                ('heading_ru', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255, null=True, verbose_name='Заглавие')),
                ('subheading', models.CharField(max_length=255, verbose_name='Подзаглавие')),
                ('subheading_bg', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие')),
                ('subheading_en', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие')),
                ('subheading_el', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие')),
                ('subheading_ru', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие')),
                ('our_history_heading', models.CharField(max_length=255, verbose_name='Заглавие на историята')),
                ('our_history_heading_bg', models.CharField(max_length=255, null=True, verbose_name='Заглавие на историята')),
                ('our_history_heading_en', models.CharField(max_length=255, null=True, verbose_name='Заглавие на историята')),
                ('our_history_heading_el', models.CharField(max_length=255, null=True, verbose_name='Заглавие на историята')),
                ('our_history_heading_ru', models.CharField(max_length=255, null=True, verbose_name='Заглавие на историята')),
                ('our_history_content', models.TextField(verbose_name='Съдържание на историята')),
                ('our_history_content_bg', models.TextField(null=True, verbose_name='Съдържание на историята')),
                ('our_history_content_en', models.TextField(null=True, verbose_name='Съдържание на историята')),
                ('our_history_content_el', models.TextField(null=True, verbose_name='Съдържание на историята')),
                ('our_history_content_ru', models.TextField(null=True, verbose_name='Съдържание на историята')),
                ('our_values_heading', models.CharField(max_length=255, verbose_name='Заглавие на ценности')),
                ('our_values_heading_bg', models.CharField(max_length=255, null=True, verbose_name='Заглавие на ценности')),
                ('our_values_heading_en', models.CharField(max_length=255, null=True, verbose_name='Заглавие на ценности')),
                ('our_values_heading_el', models.CharField(max_length=255, null=True, verbose_name='Заглавие на ценности')),
                ('our_values_heading_ru', models.CharField(max_length=255, null=True, verbose_name='Заглавие на ценности')),
                ('our_values_subheading', models.CharField(max_length=255, verbose_name='Подзаглавие на ценности')),
                ('our_values_subheading_bg', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на ценности')),
                ('our_values_subheading_en', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на ценности')),
                ('our_values_subheading_el', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на ценности')),
                ('our_values_subheading_ru', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на ценности')),
                ('our_team_heading', models.CharField(max_length=255, verbose_name='Заглавие на командата')),
                ('our_team_heading_bg', models.CharField(max_length=255, null=True, verbose_name='Заглавие на командата')),
                ('our_team_heading_en', models.CharField(max_length=255, null=True, verbose_name='Заглавие на командата')),
                ('our_team_heading_el', models.CharField(max_length=255, null=True, verbose_name='Заглавие на командата')),
                ('our_team_heading_ru', models.CharField(max_length=255, null=True, verbose_name='Заглавие на командата')),
                ('our_team_subheading', models.CharField(max_length=255, verbose_name='Подзаглавие на командата')),
                ('our_team_subheading_bg', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на командата')),
                ('our_team_subheading_en', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на командата')),
                ('our_team_subheading_el', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на командата')),
                ('our_team_subheading_ru', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на командата')),
                ('achievments_heading', models.CharField(max_length=255, verbose_name='Заглавие на успехите')),
                ('achievments_heading_bg', models.CharField(max_length=255, null=True, verbose_name='Заглавие на успехите')),
                ('achievments_heading_en', models.CharField(max_length=255, null=True, verbose_name='Заглавие на успехите')),
                ('achievments_heading_el', models.CharField(max_length=255, null=True, verbose_name='Заглавие на успехите')),
                ('achievments_heading_ru', models.CharField(max_length=255, null=True, verbose_name='Заглавие на успехите')),
                ('achievments_subheading', models.CharField(max_length=255, verbose_name='Подзаглавие на успехите')),
                ('achievments_subheading_bg', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на успехите')),
                ('achievments_subheading_en', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на успехите')),
                ('achievments_subheading_el', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на успехите')),
                ('achievments_subheading_ru', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на успехите')),
                ('cta_heading', models.CharField(max_length=255, verbose_name='Заглавие на CTA секцията')),
                ('cta_heading_bg', models.CharField(max_length=255, null=True, verbose_name='Заглавие на CTA секцията')),
                ('cta_heading_en', models.CharField(max_length=255, null=True, verbose_name='Заглавие на CTA секцията')),
                ('cta_heading_el', models.CharField(max_length=255, null=True, verbose_name='Заглавие на CTA секцията')),
                ('cta_heading_ru', models.CharField(max_length=255, null=True, verbose_name='Заглавие на CTA секцията')),
                ('cta_subheading', models.CharField(max_length=255, verbose_name='Подзаглавие на CTA секцията')),
                ('cta_subheading_bg', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на CTA секцията')),
                ('cta_subheading_en', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на CTA секцията')),
                ('cta_subheading_el', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на CTA секцията')),
                ('cta_subheading_ru', models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на CTA секцията')),
            ],
            options={
                'verbose_name': 'Страница За Нас',
                'verbose_name_plural': 'Страница За Нас',
            },
        ),
    ]
