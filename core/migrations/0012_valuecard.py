# Generated by Django 5.2.4 on 2025-07-28 10:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0011_rename_achievments_heading_siteaboutus_achievements_heading_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ValueCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('icon', models.CharField(help_text="Клас на иконата (например: 'fa-solid fa-heart') или SVG път", max_length=100, verbose_name='Икона')),
                ('title', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('title_bg', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('title_en', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('title_el', models.Char<PERSON>ield(max_length=100, null=True, verbose_name='Заглавие')),
                ('title_ru', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('description', models.TextField(verbose_name='Текст')),
                ('description_bg', models.TextField(null=True, verbose_name='Текст')),
                ('description_en', models.TextField(null=True, verbose_name='Текст')),
                ('description_el', models.TextField(null=True, verbose_name='Текст')),
                ('description_ru', models.TextField(null=True, verbose_name='Текст')),
                ('about_page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='value_cards', to='core.siteaboutus', verbose_name='Страница')),
            ],
            options={
                'verbose_name': 'Картичка с ценност',
                'verbose_name_plural': 'Картички с ценности',
            },
        ),
    ]
