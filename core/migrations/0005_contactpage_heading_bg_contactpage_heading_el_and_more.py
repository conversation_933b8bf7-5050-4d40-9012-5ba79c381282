# Generated by Django 5.2.4 on 2025-07-28 05:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_contactpage'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactpage',
            name='heading_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Заглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='heading_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Заглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='heading_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Заглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='heading_ru',
            field=models.CharField(max_length=100, null=True, verbose_name='Заглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='subheading_bg',
            field=models.CharField(max_length=100, null=True, verbose_name='Подзаглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='subheading_el',
            field=models.CharField(max_length=100, null=True, verbose_name='Подзаглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='subheading_en',
            field=models.CharField(max_length=100, null=True, verbose_name='Подзаглавие'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='subheading_ru',
            field=models.CharField(max_length=100, null=True, verbose_name='Подзаглавие'),
        ),
    ]
