# Generated by Django 5.2.4 on 2025-07-25 17:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        ('properties', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactinquiry',
            name='listing',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='properties.property', verbose_name='Имот (ако е за конкретна обява)'),
        ),
        migrations.AddField(
            model_name='whyussectioncard',
            name='section',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cards', to='core.whyussection', verbose_name='Секция'),
        ),
    ]
