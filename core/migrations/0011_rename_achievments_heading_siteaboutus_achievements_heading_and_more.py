# Generated by Django 5.2.4 on 2025-07-28 09:36

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_siteaboutus'),
    ]

    operations = [
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_heading',
            new_name='achievements_heading',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_heading_bg',
            new_name='achievements_heading_bg',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_heading_el',
            new_name='achievements_heading_el',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_heading_en',
            new_name='achievements_heading_en',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_heading_ru',
            new_name='achievements_heading_ru',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_subheading',
            new_name='achievements_subheading',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_subheading_bg',
            new_name='achievements_subheading_bg',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_subheading_el',
            new_name='achievements_subheading_el',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_subheading_en',
            new_name='achievements_subheading_en',
        ),
        migrations.RenameField(
            model_name='siteaboutus',
            old_name='achievments_subheading_ru',
            new_name='achievements_subheading_ru',
        ),
    ]
