# Generated by Django 5.2.4 on 2025-07-28 07:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_contactpage_contact_heading_contactpage_map_heading_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactpage',
            name='contact_heading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Контакт'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='contact_heading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Контакт'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='contact_heading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Контакт'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='contact_heading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Контакт'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_heading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_heading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_heading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_heading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_subheading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_subheading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_subheading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_subheading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Карта'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_heading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_heading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_heading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_heading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_subheading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_subheading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_subheading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Съобщение'),
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_subheading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие Съобщение'),
        ),
    ]
