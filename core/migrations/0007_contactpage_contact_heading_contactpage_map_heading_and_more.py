# Generated by Django 5.2.4 on 2025-07-28 07:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_alter_contactpage_heading_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactpage',
            name='contact_heading',
            field=models.CharField(default='', max_length=255, verbose_name='Заглавие Контакт'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_heading',
            field=models.CharField(default='', max_length=255, verbose_name='Заглавие Карта'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='contactpage',
            name='map_subheading',
            field=models.CharField(default='', max_length=255, verbose_name='Подзаглавие Карта'),
            preserve_default=False,
        ),
        migrations.Add<PERSON>ield(
            model_name='contactpage',
            name='send_message_heading',
            field=models.Char<PERSON>ield(default='', max_length=255, verbose_name='Заглавие Съобщение'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='contactpage',
            name='send_message_subheading',
            field=models.CharField(default='', max_length=255, verbose_name='Подзаглавие Съобщение'),
            preserve_default=False,
        ),
    ]
