# Generated by Django 5.2.4 on 2025-07-25 17:09

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactInquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име')),
                ('email', models.EmailField(max_length=254, verbose_name='Имейл адрес')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефонен номер')),
                ('message', models.TextField(verbose_name='Съобщение')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата на получаване')),
                ('is_handled', models.BooleanField(default=False, verbose_name='Обработено запитване')),
            ],
            options={
                'verbose_name': 'Запитване за контакт',
                'verbose_name_plural': 'Запитвания за контакт',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CTASection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='Активна')),
                ('heading', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('heading_bg', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_en', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_el', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_ru', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('subheading', models.CharField(max_length=100, verbose_name='Подзаглавие')),
                ('subheading_bg', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_en', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_el', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_ru', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('button_text', models.CharField(default='Научете повече', max_length=20, verbose_name='Текст на бутона')),
                ('button_link', models.CharField(default='#', max_length=100, verbose_name='Линк')),
            ],
            options={
                'verbose_name': '4. CTA',
                'verbose_name_plural': '4. CTA',
                'ordering': ['-is_active'],
            },
        ),
        migrations.CreateModel(
            name='FeaturedPropertiesSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('heading', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('heading_bg', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_en', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_el', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_ru', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('subheading', models.CharField(max_length=100, verbose_name='Подзаглавие')),
                ('subheading_bg', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_en', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_el', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_ru', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('is_active', models.BooleanField(default=False, verbose_name='Активна')),
            ],
            options={
                'verbose_name': '2. Препоръчани Имоти',
                'verbose_name_plural': '2. Препоръчани Имоти',
                'ordering': ['-is_active'],
            },
        ),
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('heading', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('heading_bg', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_en', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_el', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_ru', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('subheading', models.CharField(max_length=100, verbose_name='Подзаглавие')),
                ('subheading_bg', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_en', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_el', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_ru', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('is_active', models.BooleanField(default=False, verbose_name='Активна')),
            ],
            options={
                'verbose_name': '1. Секция',
                'verbose_name_plural': '1. Секция',
                'ordering': ['-is_active'],
            },
        ),
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Име')),
                ('name_bg', models.CharField(max_length=100, null=True, verbose_name='Име')),
                ('name_en', models.CharField(max_length=100, null=True, verbose_name='Име')),
                ('name_el', models.CharField(max_length=100, null=True, verbose_name='Име')),
                ('name_ru', models.CharField(max_length=100, null=True, verbose_name='Име')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='team_photos/', verbose_name='Снимка')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефонен номер')),
                ('email', models.EmailField(max_length=254, verbose_name='Имейл адрес')),
                ('title', models.CharField(max_length=100, verbose_name='Длъжност')),
                ('title_bg', models.CharField(max_length=100, null=True, verbose_name='Длъжност')),
                ('title_en', models.CharField(max_length=100, null=True, verbose_name='Длъжност')),
                ('title_el', models.CharField(max_length=100, null=True, verbose_name='Длъжност')),
                ('title_ru', models.CharField(max_length=100, null=True, verbose_name='Длъжност')),
            ],
            options={
                'verbose_name': 'Член на екипа',
                'verbose_name_plural': 'Членове на екипа',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=100, verbose_name='Име на клиента')),
                ('client_name_bg', models.CharField(max_length=100, null=True, verbose_name='Име на клиента')),
                ('client_name_en', models.CharField(max_length=100, null=True, verbose_name='Име на клиента')),
                ('client_name_el', models.CharField(max_length=100, null=True, verbose_name='Име на клиента')),
                ('client_name_ru', models.CharField(max_length=100, null=True, verbose_name='Име на клиента')),
                ('quote', models.TextField(verbose_name='Отзив')),
                ('quote_bg', models.TextField(null=True, verbose_name='Отзив')),
                ('quote_en', models.TextField(null=True, verbose_name='Отзив')),
                ('quote_el', models.TextField(null=True, verbose_name='Отзив')),
                ('quote_ru', models.TextField(null=True, verbose_name='Отзив')),
                ('is_active', models.BooleanField(default=True, verbose_name='Активен')),
            ],
            options={
                'verbose_name': 'Отзив',
                'verbose_name_plural': 'Отзиви',
                'ordering': ['-is_active', 'client_name'],
            },
        ),
        migrations.CreateModel(
            name='WhyUsSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='Активна')),
                ('heading', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('heading_bg', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_en', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_el', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('heading_ru', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('subheading', models.CharField(max_length=100, verbose_name='Подзаглавие')),
                ('subheading_bg', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_en', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_el', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
                ('subheading_ru', models.CharField(max_length=100, null=True, verbose_name='Подзаглавие')),
            ],
            options={
                'verbose_name': '3. Защо да изберете нас?',
                'verbose_name_plural': '3. Защо да изберете нас?',
                'ordering': ['-is_active'],
            },
        ),
        migrations.CreateModel(
            name='WhyUsSectionCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='Активна')),
                ('title', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('title_bg', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('title_en', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('title_el', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('title_ru', models.CharField(max_length=100, null=True, verbose_name='Заглавие')),
                ('description', models.TextField(verbose_name='Описание')),
                ('description_bg', models.TextField(null=True, verbose_name='Описание')),
                ('description_en', models.TextField(null=True, verbose_name='Описание')),
                ('description_el', models.TextField(null=True, verbose_name='Описание')),
                ('description_ru', models.TextField(null=True, verbose_name='Описание')),
                ('icon', models.CharField(max_length=20, verbose_name='Иконка')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Поредност')),
            ],
            options={
                'verbose_name': 'Картичка',
                'verbose_name_plural': 'Картички',
                'ordering': ['order'],
            },
        ),
    ]
