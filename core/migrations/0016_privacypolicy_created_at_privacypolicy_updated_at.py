# Generated by Django 5.2.4 on 2025-07-28 14:27

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0015_privacypolicy'),
    ]

    operations = [
        migrations.AddField(
            model_name='privacypolicy',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Дата на създаване'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='privacypolicy',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Дата на последно обновяване'),
        ),
    ]
