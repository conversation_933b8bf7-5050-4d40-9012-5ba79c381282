# Generated by Django 5.2.4 on 2025-07-28 05:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_contactpage_heading_bg_contactpage_heading_el_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contactpage',
            name='heading',
            field=models.Char<PERSON>ield(max_length=255, verbose_name='Заглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='heading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='heading_el',
            field=models.Char<PERSON>ield(max_length=255, null=True, verbose_name='Заглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='heading_en',
            field=models.Char<PERSON>ield(max_length=255, null=True, verbose_name='Заглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='heading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='subheading',
            field=models.CharField(max_length=255, verbose_name='Подзаглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='subheading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='subheading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='subheading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие'),
        ),
        migrations.AlterField(
            model_name='contactpage',
            name='subheading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие'),
        ),
    ]
