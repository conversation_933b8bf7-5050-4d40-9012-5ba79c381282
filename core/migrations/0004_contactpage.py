# Generated by Django 5.2.4 on 2025-07-28 05:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_remove_ctasection_button_link_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='Активна')),
                ('heading', models.CharField(max_length=100, verbose_name='Заглавие')),
                ('subheading', models.CharField(max_length=100, verbose_name='Подзаглавие')),
            ],
            options={
                'verbose_name': 'Страница Контакти',
                'verbose_name_plural': 'Страница Контакти',
                'ordering': ['-is_active'],
            },
        ),
    ]
