# Generated by Django 5.2.4 on 2025-07-28 10:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0012_valuecard'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_heading',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_heading_bg',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_heading_el',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_heading_en',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_heading_ru',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_subheading',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_subheading_bg',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_subheading_el',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_subheading_en',
        ),
        migrations.RemoveField(
            model_name='siteaboutus',
            name='achievements_subheading_ru',
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_heading',
            field=models.CharField(max_length=255, verbose_name='Заглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_heading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_heading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_heading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_heading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Заглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_subheading',
            field=models.CharField(max_length=255, verbose_name='Подзаглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_subheading_bg',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_subheading_el',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_subheading_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на нашия екип'),
        ),
        migrations.AlterField(
            model_name='siteaboutus',
            name='our_team_subheading_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Подзаглавие на нашия екип'),
        ),
    ]
