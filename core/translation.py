"""
Конфигурация за многоезичност с django-modeltranslation
"""

from modeltranslation.translator import register, TranslationOptions
from .models import (
    TeamMember,
    HeroSection,
    FeaturedPropertiesSection,
    WhyUsSection,
    WhyUsSectionCard,
    CTASection,
    SiteAboutUs,
    ValueCard,
    ContactPage,
    PrivacyPolicy,
    TermsOfService,
)


@register(TeamMember)
class TeamMemberTranslationOptions(TranslationOptions):
    fields = ("name", "title")


@register(HeroSection)
class HeroSectionTranslationOptions(TranslationOptions):
    fields = ("heading", "subheading")


@register(FeaturedPropertiesSection)
class FeaturedPropertiesSectionTranslationOptions(TranslationOptions):
    fields = ("heading", "subheading")


@register(WhyUsSection)
class WhyUsSectionTranslationOptions(TranslationOptions):
    fields = ("heading", "subheading")


@register(WhyUsSectionCard)
class WhyUsSectionCardTranslationOptions(TranslationOptions):
    fields = ("title", "description")


@register(CTASection)
class CTASectionTranslationOptions(TranslationOptions):
    fields = ("heading", "subheading")


@register(SiteAboutUs)
class SiteAboutUsTranslationOptions(TranslationOptions):
    fields = (
        "heading",
        "subheading",
        "our_history_heading",
        "our_history_content",
        "our_values_heading",
        "our_values_subheading",
        "our_team_heading",
        "our_team_subheading",
        "our_team_heading",
        "our_team_subheading",
        "cta_heading",
        "cta_subheading",
    )


@register(ValueCard)
class ValueCardTranslationOptions(TranslationOptions):
    fields = (
        "title",
        "description",
    )


@register(ContactPage)
class ContactPageTranslationOptions(TranslationOptions):
    fields = (
        "heading",
        "subheading",
        "contact_heading",
        "send_message_heading",
        "send_message_subheading",
        "map_heading",
        "map_subheading",
    )


@register(PrivacyPolicy)
class PrivacyPolicyTranslationOptions(TranslationOptions):
    fields = ("text",)


@register(TermsOfService)
class TermsOfServiceTranslationOptions(TranslationOptions):
    fields = ("text",)
