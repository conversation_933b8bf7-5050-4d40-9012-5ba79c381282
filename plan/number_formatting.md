# План за форматиране на числа

## 1. Анализ на текущото състояние (Готово)

- [x] Проверка на структурата на проекта.
- [x] Установяване, че `USE_I18N` е `True`, но `USE_L10N` липсва или е `False`.

## 2. Конфигурация на Django за локализация

- [ ] Активиране на `USE_L10N = True` в `config/settings.py`.
- [ ] Добавяне на `django.middleware.locale.LocaleMiddleware` към `MIDDLEWARE` в `config/settings.py`.

## 3. Прилагане на форматирането в шаблоните

- [ ] Преглед на основните шаблони (`property_detail.html`, `property_list.html`, `_property_card.html`).
- [ ] Използване на `{% localize on %}` или подходящи филтри за форматиране на числа (цени, площи и др.).
- [ ] Премахване на стари, неуспешни опити за форматиране.

## 4. Проверка на JavaScript форматиране (ако е необходимо)

- [ ] Проверка на JS файлове (`propertyGallery.js`) за манипулация на числа.
- [ ] При необходимост, използване на `Intl.NumberFormat` с езика, подаден от Django.

## 5. Тестване

- [ ] Стартиране на сървъра и проверка на форматирането при смяна на езика.
