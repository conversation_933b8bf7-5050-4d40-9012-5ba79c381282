# Language Switcher Improvements

## Overview

The language switcher has been enhanced with localStorage functionality and improved user experience features.

## New Features

### 1. localStorage Persistence
- **User preference storage**: The selected language is now saved to the browser's localStorage
- **Automatic restoration**: When users return to the site, their preferred language is automatically restored
- **Cross-tab synchronization**: Language changes are synchronized across browser tabs

### 2. Improved User Interface
- **Loading indicators**: Shows spinner animations during language switching
- **Current language highlighting**: Visual indicators show which language is currently selected
- **Checkmarks**: Current language displays a checkmark icon for better visual feedback
- **Disabled state**: Prevents multiple clicks during language switching

### 3. Better UX Practices
- **URL parameter preservation**: Query parameters are maintained when switching languages
- **Smooth transitions**: Added loading delays for better perceived performance
- **Error handling**: Graceful fallback if localStorage is not available
- **Validation**: Ensures only valid language codes are processed

## Technical Implementation

### JavaScript Component (`static/js/components/languageSwitcher.js`)

#### Key Methods:
- `initializeCurrentLanguage()`: Initializes language from localStorage or URL
- `saveLanguagePreference()`: Saves language choice to localStorage
- `switchLanguage()`: Handles language switching with loading states
- `isCurrentLanguage()`: Checks if a language is currently selected
- `navigateToLanguage()`: Navigates to the new language URL

#### localStorage Key:
- `preferredLanguage`: Stores the user's preferred language code (e.g., "bg", "en", "el", "ru")

### Template Updates (`core/templates/partials/_header.html`)

#### Desktop Version:
- Added loading spinner and disabled states
- Enhanced dropdown with checkmarks for current language
- Improved accessibility with proper ARIA labels

#### Mobile Version:
- Consistent loading indicators
- Grid layout with visual feedback
- Same functionality as desktop version

## Default Behavior

1. **First Visit**: Uses Bulgarian ("bg") as default language
2. **Subsequent Visits**: Restores user's last selected language from localStorage
3. **URL Mismatch**: If localStorage language differs from URL, redirects to preferred language
4. **Invalid Language**: Falls back to Bulgarian if invalid language code is detected

## Browser Compatibility

- **localStorage**: Supported in all modern browsers (IE8+)
- **Error Handling**: Graceful degradation if localStorage is unavailable
- **Cross-tab Sync**: Uses storage events for real-time synchronization

## Testing

New test suite added: `core/tests/test_language_switcher_localStorage.py`

Tests cover:
- localStorage functionality presence
- Loading indicators
- Current language indicators
- Multi-language support
- URL parameter preservation
- Mobile version functionality

## Usage Examples

### Checking Current Language in JavaScript
```javascript
// Get current language from localStorage
const currentLang = localStorage.getItem('preferredLanguage') || 'bg';

// Listen for language changes
window.addEventListener('storage', (event) => {
    if (event.key === 'preferredLanguage') {
        console.log('Language changed to:', event.newValue);
    }
});
```

### CSS Classes for Styling
```css
/* Loading state */
.opacity-50 { opacity: 0.5; }
.cursor-not-allowed { cursor: not-allowed; }

/* Current language highlighting */
.bg-surface-variant { background-color: var(--surface-variant); }
.text-primary { color: var(--primary); }
.font-medium { font-weight: 500; }
```

## Future Enhancements

Potential improvements for future versions:
1. **Language detection**: Auto-detect browser language on first visit
2. **Geolocation**: Suggest language based on user's location
3. **Analytics**: Track language switching patterns
4. **Preloading**: Preload content for frequently switched languages
5. **Keyboard shortcuts**: Add keyboard navigation for language switching
