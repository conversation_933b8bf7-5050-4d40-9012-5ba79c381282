# Ръководство за оптимизация на изображения

## Преглед

Системата за управление на изображения в проекта включва автоматична оптимизация, множество размери, валидация и безопасност. Тази документация описва как работи системата и как да я използвате ефективно.

## Основни функционалности

### 1. Автоматична оптимизация

-   **Преоразмеряване**: Изображенията се преоразмеряват автоматично до максимум 1920x1080px
-   **Компресия**: JPEG качество се настройва автоматично (80-90% в зависимост от размера)
-   **Корекция на ориентацията**: EXIF данните се използват за правилна ориентация
-   **Формат**: Автоматично конвертиране в подходящ формат (JPEG за снимки, PNG за прозрачност)

### 2. Множество размери (Responsive Images)

Всяко изображение се генерира в три размера:

-   **Thumbnail**: 300x200px (за карточки на имоти)
-   **Medium**: 800x600px (за галерии)
-   **Large**: 1920x1080px (за детайлни изгледи)

### 3. Подобрена структура на файловете

```
media/
└── property_images/
    └── [property_id]/
        ├── [uuid].jpg (оригинал)
        ├── [uuid]_thumbnail.jpg
        ├── [uuid]_medium.jpg
        └── [uuid]_large.jpg
```

### 4. Метаданни и SEO

-   Автоматично генериране на alt текст
-   Запазване на размери (width/height) за по-добра производителност
-   Информация за размера на файла

## Използване в темплейтите

### Карточка на имот (\_property_card.html)

```html
{% if property.main_image %}
<img
    src="{{ property.main_image.medium.url }}"
    alt="{{ property.main_image.alt_text|default:property.title }}"
    loading="lazy"
    width="{{ property.main_image.width|default:'800' }}"
    height="{{ property.main_image.height|default:'600' }}"
    class="w-full h-48 sm:h-56 object-cover"
/>
{% endif %}
```

### Детайлен изглед (property_detail.html)

```html
<!-- Главно изображение -->
<img
    x-bind:src="currentImage.url"
    x-bind:alt="currentImage.alt"
    x-bind:width="currentImage.width"
    x-bind:height="currentImage.height"
    loading="lazy"
    class="w-full h-96 object-cover"
/>

<!-- Миниатюри -->
<img
    src="{{ image.thumbnail.url }}"
    alt="{{ image.alt_text|default:'Снимка '|add:forloop.counter }}"
    loading="lazy"
    class="w-full h-full object-cover"
/>
```

## Валидация и безопасност

### Разрешени формати

-   JPEG (.jpg, .jpeg)
-   PNG (.png)
-   WebP (.webp)

### Ограничения

-   **Максимален размер на файла**: 10MB
-   **Минимални размери**: 300x200 пиксела
-   **Максимални размери**: 5000x5000 пиксела

### Проверки за безопасност

-   Валидация на MIME типа
-   Проверка на файловото разширение
-   Верификация на изображението с PIL
-   Почистване на имената на файловете

## Админ панел

### Подобрения в PropertyImageInline

-   **Преглед на миниатюри**: Автоматично показване на малки изображения
-   **Информация за файла**: Размери и размер на файла
-   **Валидация**: Реално време проверка при качване

### Полета в админ панела

-   `image`: Основното изображение
-   `order`: Поредност (1 = главна снимка)
-   `alt_text`: Алтернативен текст (автоматично генериран ако не е зададен)
-   `image_preview`: Миниатюра (само за четене)
-   `image_info`: Размери и размер на файла (само за четене)

## API за разработчици

### Основни утилити (core/utils.py)

#### optimize_image()

```python
from core.utils import optimize_image

# Оптимизира изображение
optimized = optimize_image(
    image_file,
    max_width=1920,
    max_height=1080,
    quality=85
)
```

#### validate_image_file()

```python
from core.utils import validate_image_file

# Валидира качен файл
is_valid, error_message = validate_image_file(uploaded_file)
if not is_valid:
    print(f"Грешка: {error_message}")
```

#### get_image_info()

```python
from core.utils import get_image_info

# Извлича информация за изображението
info = get_image_info(image_file)
print(f"Размери: {info['width']}x{info['height']}")
```

## Производителност

### Оптимизации

-   **Lazy loading**: Изображенията се зареждат при нужда
-   **Responsive images**: Правилният размер за всяко устройство
-   **Компресия**: Балансирано качество и размер на файла
-   **Кеширане**: ImageKit автоматично кешира генерираните размери

### Препоръки за продукция

1. **CDN**: Използвайте CDN за статични файлове
2. **WebP**: Разгледайте добавяне на WebP поддръжка
3. **Мониторинг**: Следете размера на media директорията
4. **Backup**: Редовно архивирайте изображенията

## Настройки (settings.py)

```python
# Ограничения за качване
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_PERMISSIONS = 0o644

# Разрешени формати
ALLOWED_IMAGE_EXTENSIONS = [".jpg", ".jpeg", ".png", ".webp"]
ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/webp"]
```

## Тестване

### Стартиране на тестовете

```bash
# Всички тестове за изображения
python manage.py test core.tests.test_image_processing

# Конкретен тест
python manage.py test core.tests.test_image_processing.ImageProcessingTestCase.test_optimize_image_basic
```

### Тестови случаи

-   Оптимизация на изображения
-   Валидация на файлове
-   Създаване на PropertyImage обекти
-   Автоматично генериране на alt текст
-   Изчисляване на съотношения

## Отстраняване на проблеми

### Чести проблеми

#### "Файлът е твърде голям"

-   Проверете FILE_UPLOAD_MAX_MEMORY_SIZE в settings.py
-   Уверете се, че изображението е под 10MB

#### "Изображението е твърде малко"

-   Минималните размери са 300x200 пиксела
-   Използвайте по-голямо изображение

#### "Неподдържан формат"

-   Поддържат се само JPEG, PNG и WebP
-   Конвертирайте изображението в подходящ формат

#### "'ImageFieldFile' object has no attribute 'content_type'"

-   Тази грешка е решена в системата
-   Валидацията работи както за нови файлове (UploadedFile), така и за съществуващи (ImageFieldFile)
-   Оптимизацията се прилага само при качване на нови изображения

#### Проблеми с производителността

-   Проверете дали ImageKit кешът работи правилно
-   Разгледайте използването на CDN
-   Мониторирайте размера на media директорията

## Бъдещи подобрения

### Планирани функционалности

-   Масово качване на изображения
-   Автоматично генериране на WebP варианти
-   Интеграция с облачни услуги (AWS S3, Cloudinary)
-   Водни знаци
-   Автоматично подрязване (smart cropping)

### Възможни разширения

-   Поддръжка за AVIF формат
-   Машинно обучение за автоматично тагване
-   Интеграция с AI услуги за описания
-   Автоматично оптимизиране на стари изображения
