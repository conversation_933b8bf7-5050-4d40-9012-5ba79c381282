# 📦 Миграция от SQLite към PostgreSQL в Django

Това ръководство описва стъпките за безопасно прехвърляне на данни от SQLite база (`db.sqlite3`) към PostgreSQL с помощта на вградените инструменти на Django.

---

## 🧩 Предварителни изисквания

- PostgreSQL е инсталиран и работи.
- Създадена е празна база в PostgreSQL.
- Инсталиран е `psycopg` драйвър:
  ```bash
  pip install psycopg[binary]
  ```

---

## 📁 1. Експортиране на данните от SQLite

Увери се, че проектът все още ползва SQLite и изпълни:

```bash
python manage.py dumpdata \
  --natural-primary \
  --natural-foreign \
  --exclude=contenttypes \
  --exclude=auth.Permission \
  --indent 2 > data.json
```

📌 Това ще създаде четим JSON файл с всички данни от проекта, без системните таблици.

---

## 🔁 2. Преход към PostgreSQL

### 2.1 Промени `DATABASES` в `settings.py`:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'името_на_новата_база',
        'USER': 'потребител',
        'PASSWORD': 'парола',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### 2.2 Изтрий или преименувай `db.sqlite3`:

```bash
rm db.sqlite3
# или
mv db.sqlite3 db.sqlite3.bak
```

---

## 🏗️ 3. Създай схемата в PostgreSQL

Създай празната база структура чрез миграции:

```bash
python manage.py migrate
```

---

## 📥 4. Импортирай данните

```bash
python manage.py loaddata data.json
```

📌 Увери се, че не излизат грешки при зареждането.

---

## ✅ 5. Провери всичко

- Влез в Django Admin и провери дали данните са налични.
- Увери се, че потребителите, продуктите, статиите и т.н. са налице.
- Провери дали всичко работи нормално.

---

## 🛡️ Допълнителни съвети

- **Направи резервно копие** на `data.json`.
- **Използвай `.bak` файл на SQLite**, за да се върнеш при нужда.
- **Ако използваш `BigAutoField`** в новата база, внимавай с `id` типовете.

---

## 🎉 Готово!

Вече успешно използваш PostgreSQL вместо SQLite!
